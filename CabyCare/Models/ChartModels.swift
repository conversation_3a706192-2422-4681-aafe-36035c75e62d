import Foundation
import SwiftUI

// MARK: - Chart Time Period
enum ChartTimePeriod: String, CaseIterable {
    case day = "day"
    case week = "week"
    case month = "month"
    
    var displayName: String {
        switch self {
        case .day:
            return NSLocalizedString("chart_period_day", comment: "Day")
        case .week:
            return NSLocalizedString("chart_period_week", comment: "Week")
        case .month:
            return NSLocalizedString("chart_period_month", comment: "Month")
        }
    }
    
    var icon: String {
        switch self {
        case .day:
            return "calendar"
        case .week:
            return "calendar.badge.clock"
        case .month:
            return "calendar.badge.plus"
        }
    }
    
    // 默认显示的数据点数量
    var defaultDataPoints: Int {
        switch self {
        case .day:
            return 7  // 显示7天
        case .week:
            return 4  // 显示4周
        case .month:
            return 6  // 显示6个月
        }
    }
    
    // 获取时间间隔
    var timeInterval: TimeInterval {
        switch self {
        case .day:
            return 24 * 60 * 60  // 1天
        case .week:
            return 7 * 24 * 60 * 60  // 1周
        case .month:
            return 30 * 24 * 60 * 60  // 1个月（近似）
        }
    }
}

// MARK: - Chart Data Point
struct ToiletChartDataPoint: Identifiable {
    let id = UUID()
    let date: Date
    let count: Int
    let duration: TimeInterval // 总时长（秒）
    
    // 格式化的日期显示
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter.string(from: date)
    }
    
    // 格式化的时长显示（分钟）
    var formattedDuration: String {
        let minutes = Int(duration) / 60
        return "\(minutes)分钟"
    }
}

// MARK: - Cat Chart Data
struct CatChartData: Identifiable {
    let id = UUID()
    let catId: String
    let catName: String
    let avatarUrl: String?
    let dataPoints: [ToiletChartDataPoint]
    let timePeriod: ChartTimePeriod
    let dateRange: DateRange
    
    // 计算统计信息
    var totalCount: Int {
        dataPoints.reduce(0) { $0 + $1.count }
    }
    
    var averageCount: Double {
        guard !dataPoints.isEmpty else { return 0 }
        return Double(totalCount) / Double(dataPoints.count)
    }
    
    var maxCount: Int {
        dataPoints.map { $0.count }.max() ?? 0
    }
    
    var minCount: Int {
        dataPoints.map { $0.count }.min() ?? 0
    }
    
    // 获取趋势
    var trend: ChartTrend {
        guard dataPoints.count >= 2 else { return .stable }
        
        let firstHalf = dataPoints.prefix(dataPoints.count / 2)
        let secondHalf = dataPoints.suffix(dataPoints.count / 2)
        
        let firstAverage = firstHalf.reduce(0) { $0 + $1.count } / firstHalf.count
        let secondAverage = secondHalf.reduce(0) { $0 + $1.count } / secondHalf.count
        
        let difference = secondAverage - firstAverage
        
        if difference > 0 {
            return .increasing
        } else if difference < 0 {
            return .decreasing
        } else {
            return .stable
        }
    }
}

// MARK: - Chart Trend
enum ChartTrend {
    case increasing
    case decreasing
    case stable
    
    var color: Color {
        switch self {
        case .increasing:
            return .green
        case .decreasing:
            return .orange
        case .stable:
            return .blue
        }
    }
    
    var icon: String {
        switch self {
        case .increasing:
            return "arrow.up.right"
        case .decreasing:
            return "arrow.down.right"
        case .stable:
            return "arrow.right"
        }
    }
    
    var description: String {
        switch self {
        case .increasing:
            return NSLocalizedString("chart_trend_increasing", comment: "Increasing")
        case .decreasing:
            return NSLocalizedString("chart_trend_decreasing", comment: "Decreasing")
        case .stable:
            return NSLocalizedString("chart_trend_stable", comment: "Stable")
        }
    }
}

// MARK: - Date Range
struct DateRange: Equatable {
    let startDate: Date
    let endDate: Date
    
    var duration: TimeInterval {
        endDate.timeIntervalSince(startDate)
    }
    
    var formattedRange: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return "\(formatter.string(from: startDate)) - \(formatter.string(from: endDate))"
    }
    
    // 创建默认的日期范围
    static func defaultRange(for period: ChartTimePeriod) -> DateRange {
        let endDate = Date()
        let startDate = Calendar.current.date(
            byAdding: .day,
            value: -period.defaultDataPoints,
            to: endDate
        ) ?? endDate
        
        return DateRange(startDate: startDate, endDate: endDate)
    }
    
    // 创建自定义日期范围
    static func customRange(startDate: Date, endDate: Date) -> DateRange {
        return DateRange(startDate: startDate, endDate: endDate)
    }
}

// MARK: - Chart Configuration
struct ChartConfiguration {
    let showDataPoints: Bool
    let showGrid: Bool
    let animationEnabled: Bool
    let primaryColor: Color
    let secondaryColor: Color
    let backgroundColor: Color
    
    static let `default` = ChartConfiguration(
        showDataPoints: true,
        showGrid: true,
        animationEnabled: true,
        primaryColor: .blue,
        secondaryColor: .gray,
        backgroundColor: .clear
    )
    
    static func themed(for colorScheme: ColorScheme) -> ChartConfiguration {
        return ChartConfiguration(
            showDataPoints: true,
            showGrid: true,
            animationEnabled: true,
            primaryColor: colorScheme == .dark ? .themeSecondary : .themePrimary,
            secondaryColor: colorScheme == .dark ? Color.gray.opacity(0.8) : Color.gray.opacity(0.6),
            backgroundColor: .clear
        )
    }
}

// MARK: - Chart View State
class ChartViewState: ObservableObject {
    @Published var selectedPeriod: ChartTimePeriod = .day
    @Published var customDateRange: DateRange?
    @Published var isLoading: Bool = false
    @Published var error: String?
    
    var currentDateRange: DateRange {
        return customDateRange ?? DateRange.defaultRange(for: selectedPeriod)
    }
    
    func updatePeriod(_ period: ChartTimePeriod) {
        selectedPeriod = period
        customDateRange = nil // 重置自定义范围
    }
    
    func updateCustomRange(_ range: DateRange) {
        customDateRange = range
    }
    
    func resetToDefault() {
        selectedPeriod = .day
        customDateRange = nil
        error = nil
    }
}
