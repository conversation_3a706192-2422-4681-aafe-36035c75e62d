import Foundation

// MARK: - Chart Data Service
@MainActor
class ChartDataService: ObservableObject {
    static let shared = ChartDataService()

    private let cacheManager = ChartCacheManager.shared
    private let videoPlayerViewModel = VideoPlayerViewModel.shared

    private init() {}
    
    // MARK: - Public Methods
    
    /// 为指定猫咪生成图表数据（使用智能缓存）
    func generateChartData(
        for cat: CatProfile,
        dataType: ChartDataType,
        timePeriod: ChartTimePeriod,
        dateRange: DateRange
    ) async -> CatChartData {
        Log.info("🔄 开始为猫咪 \(cat.name) 生成\(dataType.displayName)图表数据，时间段: \(timePeriod.rawValue)")

        // 确保有足够的历史数据
        await ensureDataAvailable(for: cat.id, in: dateRange)

        switch dataType {
        case .toilet:
            // 根据时间段聚合如厕数据
            let dataPoints = await aggregateToiletDataFromCache(catId: cat.id, timePeriod: timePeriod, dateRange: dateRange)

            let chartData = CatChartData(
                catId: cat.id,
                catName: cat.name,
                avatarUrl: cat.avatarUrl,
                dataPoints: dataPoints,
                timePeriod: timePeriod,
                dateRange: dateRange
            )

            Log.info("✅ 猫咪 \(cat.name) 如厕图表数据生成完成，共 \(dataPoints.count) 个数据点")
            return chartData

        case .weight:
            // 根据时间段聚合重量数据
            let weightDataPoints = await aggregateWeightDataFromCache(catId: cat.id, timePeriod: timePeriod, dateRange: dateRange)

            let chartData = CatChartData(
                catId: cat.id,
                catName: cat.name,
                avatarUrl: cat.avatarUrl,
                weightDataPoints: weightDataPoints,
                timePeriod: timePeriod,
                dateRange: dateRange
            )

            Log.info("✅ 猫咪 \(cat.name) 重量图表数据生成完成，共 \(weightDataPoints.count) 个数据点")
            return chartData
        }
    }

    /// 为指定猫咪生成图表数据（兼容旧版本）
    func generateChartData(
        for cat: CatProfile,
        timePeriod: ChartTimePeriod,
        dateRange: DateRange
    ) async -> CatChartData {
        return await generateChartData(for: cat, dataType: .toilet, timePeriod: timePeriod, dateRange: dateRange)
    }
    
    /// 批量生成多只猫咪的图表数据（优化版本）
    func generateChartDataForAllCats(
        cats: [CatProfile],
        dataType: ChartDataType,
        timePeriod: ChartTimePeriod,
        dateRange: DateRange
    ) async -> [CatChartData] {
        Log.info("🔄 开始为 \(cats.count) 只猫咪生成\(dataType.displayName)图表数据")

        var chartDataList: [CatChartData] = []

        for cat in cats {
            let chartData = await generateChartData(
                for: cat,
                dataType: dataType,
                timePeriod: timePeriod,
                dateRange: dateRange
            )
            chartDataList.append(chartData)
        }

        Log.info("✅ 所有猫咪的\(dataType.displayName)图表数据生成完成")
        return chartDataList
    }

    /// 批量生成多只猫咪的图表数据（兼容旧版本）
    func generateChartDataForAllCats(
        cats: [CatProfile],
        timePeriod: ChartTimePeriod,
        dateRange: DateRange
    ) async -> [CatChartData] {
        return await generateChartDataForAllCats(cats: cats, dataType: .toilet, timePeriod: timePeriod, dateRange: dateRange)
    }

    /// 预加载所有猫咪的数据（批量优化）
    private func preloadDataForAllCats(_ catIds: [String], in dateRange: DateRange) async {
        Log.info("📥 开始批量预加载 \(catIds.count) 只猫咪的数据")

        // 收集所有猫咪缺失的日期
        var allMissingDates: Set<Date> = []
        var catMissingDates: [String: [Date]] = [:]

        for catId in catIds {
            let missingDates = cacheManager.getMissingDates(for: catId, in: dateRange)
            catMissingDates[catId] = missingDates
            allMissingDates.formUnion(missingDates)
        }

        if allMissingDates.isEmpty {
            Log.info("✅ 所有数据已缓存，无需预加载")
            return
        }

        Log.info("📥 需要获取 \(allMissingDates.count) 个不同日期的数据")

        // 按日期批量获取数据（所有猫咪一起获取）
        let sortedDates = Array(allMissingDates).sorted()
        await fetchBatchDataForAllCats(catIds: catIds, dates: sortedDates, catMissingDates: catMissingDates)
    }

    /// 为所有猫咪批量获取数据
    private func fetchBatchDataForAllCats(
        catIds: [String],
        dates: [Date],
        catMissingDates: [String: [Date]]
    ) async {
        let batchSize = ChartCacheConfiguration.maxBatchDays

        for i in stride(from: 0, to: dates.count, by: batchSize) {
            let endIndex = min(i + batchSize, dates.count)
            let batchDates = Array(dates[i..<endIndex])

            guard let startDate = batchDates.first,
                  let endDate = batchDates.last else { continue }

            let calendar = Calendar.current
            let batchStartDate = calendar.startOfDay(for: startDate)
            let batchEndDate = calendar.date(byAdding: .day, value: 1, to: calendar.startOfDay(for: endDate)) ?? endDate

            Log.info("📥 批量获取所有猫咪数据: \(batchStartDate.formattedString()) 到 \(batchEndDate.formattedString())")

            // 为每只猫咪获取这个时间范围的数据
            await withTaskGroup(of: Void.self) { group in
                for catId in catIds {
                    // 只为需要这些日期数据的猫咪获取
                    let catNeedsDates = catMissingDates[catId] ?? []
                    let relevantDates = batchDates.filter { catNeedsDates.contains($0) }

                    if !relevantDates.isEmpty {
                        group.addTask {
                            let segments = await self.fetchSegmentsFromAPI(
                                for: catId,
                                from: batchStartDate,
                                to: batchEndDate
                            )
                            await self.processBatchSegments(segments, for: catId, in: relevantDates)
                        }
                    }
                }
            }
        }
    }
    
    // MARK: - Private Methods

    /// 确保指定时间范围的数据可用（智能缓存策略）
    private func ensureDataAvailable(for catId: String, in dateRange: DateRange) async {
        Log.info("🔄 检查猫咪 \(catId) 在 \(dateRange.formattedRange) 的数据可用性")

        // 获取缺失的日期
        let missingDates = cacheManager.getMissingDates(for: catId, in: dateRange)

        if missingDates.isEmpty {
            Log.info("✅ 所有数据已缓存，无需获取新数据")
            return
        }

        Log.info("📥 需要获取 \(missingDates.count) 天的数据")

        // 批量获取缺失的数据
        await fetchMissingData(for: catId, dates: missingDates)
    }

    /// 批量获取缺失的数据
    private func fetchMissingData(for catId: String, dates: [Date]) async {
        // 按批次获取数据，避免单次请求过多
        let batchSize = ChartCacheConfiguration.maxBatchDays
        let sortedDates = dates.sorted()

        for i in stride(from: 0, to: sortedDates.count, by: batchSize) {
            let endIndex = min(i + batchSize, sortedDates.count)
            let batchDates = Array(sortedDates[i..<endIndex])

            guard let startDate = batchDates.first,
                  let endDate = batchDates.last else { continue }

            // 扩展到完整的天
            let calendar = Calendar.current
            let batchStartDate = calendar.startOfDay(for: startDate)
            let batchEndDate = calendar.date(byAdding: .day, value: 1, to: calendar.startOfDay(for: endDate)) ?? endDate

            Log.info("📥 批量获取数据: \(batchStartDate.formattedString()) 到 \(batchEndDate.formattedString())")

            // 获取这个时间范围的数据
            let segments = await fetchSegmentsFromAPI(for: catId, from: batchStartDate, to: batchEndDate)

            // 按天组织数据并缓存
            await processBatchSegments(segments, for: catId, in: batchDates)
        }
    }

    /// 从API获取视频段数据
    private func fetchSegmentsFromAPI(for catId: String, from startDate: Date, to endDate: Date) async -> [VideoSegment] {
        // 使用VideoPlayerViewModel的方法获取数据
        let segments = await videoPlayerViewModel.getToiletSegments(for: catId, from: startDate, to: endDate)

        Log.info("📥 从API获取到 \(segments.count) 个视频段")
        return segments
    }

    /// 处理批量获取的视频段数据
    private func processBatchSegments(_ segments: [VideoSegment], for catId: String, in dates: [Date]) async {
        let calendar = Calendar.current
        var dailyDataList: [DailyToiletData] = []

        // 按天组织数据
        for date in dates {
            let startOfDay = calendar.startOfDay(for: date)
            let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? startOfDay

            // 过滤出当天的如厕记录
            let dailySegments = segments.filter { segment in
                segment.start >= startOfDay && segment.start < endOfDay &&
                isToiletSegment(segment, for: catId)
            }

            // 创建每日数据
            let dailyData = DailyToiletData(date: date, catId: catId, segments: dailySegments)
            dailyDataList.append(dailyData)

            Log.debug("📊 \(date.formattedString()): \(dailySegments.count) 次如厕")
        }

        // 批量保存到缓存
        cacheManager.saveDailyDataBatch(dailyDataList)
    }
    
    /// 判断是否为如厕记录（简化版本，主要依赖animal_id）
    private func isToiletSegment(_ segment: VideoSegment, for catId: String) -> Bool {
        // 如果有明确的animal_id，直接匹配
        if let animalId = segment.animal_id, animalId == catId {
            return true
        }

        // 如果没有animal_id，使用缓存的猫咪数据进行匹配
        let cachedData = videoPlayerViewModel.getCachedDataForStatistics()
        guard let cat = cachedData.catsCache[catId],
              let weightString = cat.weight.components(separatedBy: "kg").first,
              let expectedWeight = Double(weightString) else {
            return false
        }

        // 体重匹配范围（±20%）
        let weightTolerance = expectedWeight * 0.2
        let weightMatch = abs(segment.weight_cat - expectedWeight) <= weightTolerance

        // 时长合理范围（10秒到1小时）
        let durationMatch = segment.duration >= 10 && segment.duration <= 3600

        return weightMatch && durationMatch
    }
    
    /// 基于缓存数据聚合如厕图表数据点
    private func aggregateToiletDataFromCache(
        catId: String,
        timePeriod: ChartTimePeriod,
        dateRange: DateRange
    ) async -> [ToiletChartDataPoint] {
        Log.info("📊 开始聚合缓存数据: \(timePeriod.rawValue)")

        switch timePeriod {
        case .day:
            return await aggregateDailyData(catId: catId, dateRange: dateRange)
        case .week:
            return await aggregateWeeklyData(catId: catId, dateRange: dateRange)
        case .month:
            return await aggregateMonthlyData(catId: catId, dateRange: dateRange)
        }
    }

    /// 聚合每日数据
    private func aggregateDailyData(catId: String, dateRange: DateRange) async -> [ToiletChartDataPoint] {
        let dailyDataList = cacheManager.getDailyDataRange(for: catId, in: dateRange)

        return dailyDataList.map { dailyData in
            ToiletChartDataPoint(
                date: dailyData.dateObject ?? Date(),
                count: dailyData.totalCount,
                duration: dailyData.totalDuration
            )
        }
    }

    /// 聚合每周数据（显示8周，从周一到周日）
    private func aggregateWeeklyData(catId: String, dateRange: DateRange) async -> [ToiletChartDataPoint] {
        let calendar = Calendar.current
        var dataPoints: [ToiletChartDataPoint] = []

        // 计算8周的时间范围
        let endDate = Date()
        let startDate = calendar.date(byAdding: .weekOfYear, value: -7, to: endDate) ?? endDate // 8周前（包含当前周）

        // 调整到周边界
        let weekStartDate = calendar.startOfWeek(for: startDate)
        let weekEndDate = calendar.endOfWeek(for: endDate)

        Log.info("📊 周数据聚合范围: \(weekStartDate.formattedString()) 到 \(weekEndDate.formattedString())")

        // 获取扩展的每日数据范围
        let extendedRange = DateRange(startDate: weekStartDate, endDate: weekEndDate)
        let dailyDataList = cacheManager.getDailyDataRange(for: catId, in: extendedRange)

        // 生成8个完整的周
        var currentWeekStart = weekStartDate
        for weekIndex in 0..<8 {
            let weekEnd = calendar.endOfWeek(for: currentWeekStart)

            // 过滤出当前周的数据
            let weekData = dailyDataList.filter { dailyData in
                guard let date = dailyData.dateObject else { return false }
                return date >= currentWeekStart && date <= weekEnd
            }

            // 计算当前周的统计数据
            let totalCount = weekData.reduce(0) { $0 + $1.totalCount }
            let totalDuration = weekData.reduce(0) { $0 + $1.totalDuration }

            let dataPoint = ToiletChartDataPoint(
                date: currentWeekStart,
                count: totalCount,
                duration: totalDuration
            )

            dataPoints.append(dataPoint)

            Log.debug("📊 第\(weekIndex + 1)周 (\(currentWeekStart.formattedString())): \(totalCount)次, \(weekData.count)天数据")

            // 移动到下一周
            currentWeekStart = calendar.date(byAdding: .weekOfYear, value: 1, to: currentWeekStart) ?? currentWeekStart
        }

        Log.info("📊 周数据聚合完成: \(dataPoints.count) 个数据点")
        return dataPoints
    }

    /// 聚合每月数据（显示8个月，从月初到月末）
    private func aggregateMonthlyData(catId: String, dateRange: DateRange) async -> [ToiletChartDataPoint] {
        let calendar = Calendar.current
        var dataPoints: [ToiletChartDataPoint] = []

        // 计算8个月的时间范围
        let endDate = Date()
        let startDate = calendar.date(byAdding: .month, value: -7, to: endDate) ?? endDate // 8个月前（包含当前月）

        // 调整到月边界
        let monthStartDate = calendar.monthStart(for: startDate)
        let monthEndDate = calendar.endOfMonth(for: endDate)

        Log.info("📊 月数据聚合范围: \(monthStartDate.formattedString()) 到 \(monthEndDate.formattedString())")

        // 获取扩展的每日数据范围
        let extendedRange = DateRange(startDate: monthStartDate, endDate: monthEndDate)
        let dailyDataList = cacheManager.getDailyDataRange(for: catId, in: extendedRange)

        // 生成8个完整的月
        var currentMonthStart = monthStartDate
        for monthIndex in 0..<8 {
            let monthEnd = calendar.endOfMonth(for: currentMonthStart)

            // 过滤出当前月的数据
            let monthData = dailyDataList.filter { dailyData in
                guard let date = dailyData.dateObject else { return false }
                return date >= currentMonthStart && date <= monthEnd
            }

            // 计算当前月的统计数据
            let totalCount = monthData.reduce(0) { $0 + $1.totalCount }
            let totalDuration = monthData.reduce(0) { $0 + $1.totalDuration }

            let dataPoint = ToiletChartDataPoint(
                date: currentMonthStart,
                count: totalCount,
                duration: totalDuration
            )

            dataPoints.append(dataPoint)

            // 格式化月份显示
            let monthFormatter = DateFormatter()
            monthFormatter.dateFormat = "yyyy年MM月"
            let monthString = monthFormatter.string(from: currentMonthStart)

            Log.debug("📊 第\(monthIndex + 1)月 (\(monthString)): \(totalCount)次, \(monthData.count)天数据")

            // 移动到下一个月
            currentMonthStart = calendar.date(byAdding: .month, value: 1, to: currentMonthStart) ?? currentMonthStart
        }

        Log.info("📊 月数据聚合完成: \(dataPoints.count) 个数据点")
        return dataPoints
    }

    // MARK: - Weight Data Aggregation

    /// 基于缓存数据聚合重量图表数据点
    private func aggregateWeightDataFromCache(
        catId: String,
        timePeriod: ChartTimePeriod,
        dateRange: DateRange
    ) async -> [WeightChartDataPoint] {
        Log.info("📊 开始聚合重量缓存数据: \(timePeriod.rawValue)")

        switch timePeriod {
        case .day:
            return await aggregateDailyWeightData(catId: catId, dateRange: dateRange)
        case .week:
            return await aggregateWeeklyWeightData(catId: catId, dateRange: dateRange)
        case .month:
            return await aggregateMonthlyWeightData(catId: catId, dateRange: dateRange)
        }
    }

    /// 聚合每日重量数据
    private func aggregateDailyWeightData(catId: String, dateRange: DateRange) async -> [WeightChartDataPoint] {
        let dailyDataList = cacheManager.getDailyDataRange(for: catId, in: dateRange)

        return dailyDataList.compactMap { dailyData in
            // 获取该日的所有视频段
            let segments = dailyData.segments
            let weights = segments.compactMap { segment in
                // 只考虑有效的重量数据
                segment.weight_cat > 0.5 && segment.weight_cat < 20.0 ? segment.weight_cat : nil
            }

            guard !weights.isEmpty else { return nil }

            let averageWeight = weights.reduce(0.0, +) / Double(weights.count)
            let minWeight = weights.min()
            let maxWeight = weights.max()

            return WeightChartDataPoint(
                date: dailyData.dateObject ?? Date(),
                averageWeight: averageWeight,
                minWeight: minWeight,
                maxWeight: maxWeight,
                recordCount: weights.count
            )
        }
    }

    /// 聚合每周重量数据
    private func aggregateWeeklyWeightData(catId: String, dateRange: DateRange) async -> [WeightChartDataPoint] {
        let calendar = Calendar.current
        var dataPoints: [WeightChartDataPoint] = []

        // 计算8周的时间范围
        let endDate = Date()
        let startDate = calendar.date(byAdding: .weekOfYear, value: -7, to: endDate) ?? endDate

        // 调整到周边界
        let weekStartDate = calendar.startOfWeek(for: startDate)
        let weekEndDate = calendar.endOfWeek(for: endDate)

        // 获取扩展的每日数据范围
        let extendedRange = DateRange(startDate: weekStartDate, endDate: weekEndDate)
        let dailyDataList = cacheManager.getDailyDataRange(for: catId, in: extendedRange)

        // 生成8个完整的周
        var currentWeekStart = weekStartDate
        for weekIndex in 0..<8 {
            let weekEnd = calendar.endOfWeek(for: currentWeekStart)

            // 过滤出当前周的数据
            let weekData = dailyDataList.filter { dailyData in
                guard let date = dailyData.dateObject else { return false }
                return date >= currentWeekStart && date <= weekEnd
            }

            // 收集当前周的所有重量数据
            var allWeights: [Double] = []
            for dailyData in weekData {
                let weights = dailyData.segments.compactMap { segment in
                    segment.weight_cat > 0.5 && segment.weight_cat < 20.0 ? segment.weight_cat : nil
                }
                allWeights.append(contentsOf: weights)
            }

            if !allWeights.isEmpty {
                let averageWeight = allWeights.reduce(0.0, +) / Double(allWeights.count)
                let minWeight = allWeights.min()
                let maxWeight = allWeights.max()

                let dataPoint = WeightChartDataPoint(
                    date: currentWeekStart,
                    averageWeight: averageWeight,
                    minWeight: minWeight,
                    maxWeight: maxWeight,
                    recordCount: allWeights.count
                )

                dataPoints.append(dataPoint)
            } else {
                // 如果没有数据，创建一个空的数据点
                let dataPoint = WeightChartDataPoint(
                    date: currentWeekStart,
                    averageWeight: 0.0,
                    minWeight: nil,
                    maxWeight: nil,
                    recordCount: 0
                )
                dataPoints.append(dataPoint)
            }

            // 移动到下一周
            currentWeekStart = calendar.date(byAdding: .weekOfYear, value: 1, to: currentWeekStart) ?? currentWeekStart
        }

        Log.info("📊 周重量数据聚合完成: \(dataPoints.count) 个数据点")
        return dataPoints
    }

    /// 聚合每月重量数据
    private func aggregateMonthlyWeightData(catId: String, dateRange: DateRange) async -> [WeightChartDataPoint] {
        let calendar = Calendar.current
        var dataPoints: [WeightChartDataPoint] = []

        // 计算8个月的时间范围
        let endDate = Date()
        let startDate = calendar.date(byAdding: .month, value: -7, to: endDate) ?? endDate

        // 调整到月边界
        let monthStartDate = calendar.monthStart(for: startDate)
        let monthEndDate = calendar.endOfMonth(for: endDate)

        // 获取扩展的每日数据范围
        let extendedRange = DateRange(startDate: monthStartDate, endDate: monthEndDate)
        let dailyDataList = cacheManager.getDailyDataRange(for: catId, in: extendedRange)

        // 生成8个完整的月
        var currentMonthStart = monthStartDate
        for monthIndex in 0..<8 {
            let monthEnd = calendar.endOfMonth(for: currentMonthStart)

            // 过滤出当前月的数据
            let monthData = dailyDataList.filter { dailyData in
                guard let date = dailyData.dateObject else { return false }
                return date >= currentMonthStart && date <= monthEnd
            }

            // 收集当前月的所有重量数据
            var allWeights: [Double] = []
            for dailyData in monthData {
                let weights = dailyData.segments.compactMap { segment in
                    segment.weight_cat > 0.5 && segment.weight_cat < 20.0 ? segment.weight_cat : nil
                }
                allWeights.append(contentsOf: weights)
            }

            if !allWeights.isEmpty {
                let averageWeight = allWeights.reduce(0.0, +) / Double(allWeights.count)
                let minWeight = allWeights.min()
                let maxWeight = allWeights.max()

                let dataPoint = WeightChartDataPoint(
                    date: currentMonthStart,
                    averageWeight: averageWeight,
                    minWeight: minWeight,
                    maxWeight: maxWeight,
                    recordCount: allWeights.count
                )

                dataPoints.append(dataPoint)
            } else {
                // 如果没有数据，创建一个空的数据点
                let dataPoint = WeightChartDataPoint(
                    date: currentMonthStart,
                    averageWeight: 0.0,
                    minWeight: nil,
                    maxWeight: nil,
                    recordCount: 0
                )
                dataPoints.append(dataPoint)
            }

            // 移动到下一个月
            currentMonthStart = calendar.date(byAdding: .month, value: 1, to: currentMonthStart) ?? currentMonthStart
        }

        Log.info("📊 月重量数据聚合完成: \(dataPoints.count) 个数据点")
        return dataPoints
    }

    /// 预加载指定时间段的数据
    func preloadDataForPeriod(_ timePeriod: ChartTimePeriod, for catIds: [String]) async {
        let dataRange = getOptimalDataRange(for: timePeriod)

        Log.info("📊 开始预加载 \(timePeriod.rawValue) 数据，范围: \(dataRange.formattedRange)")

        for catId in catIds {
            await ensureDataAvailable(for: catId, in: dataRange)
        }

        Log.info("✅ 预加载完成")
    }

    /// 获取优化的数据范围（确保边界对齐）
    private func getOptimalDataRange(for timePeriod: ChartTimePeriod) -> DateRange {
        let calendar = Calendar.current
        let endDate = Date()

        switch timePeriod {
        case .day:
            // 7天数据
            let startDate = calendar.date(byAdding: .day, value: -6, to: endDate) ?? endDate
            return DateRange(startDate: calendar.startOfDay(for: startDate), endDate: endDate)

        case .week:
            // 8周数据，对齐到周边界
            let startDate = calendar.date(byAdding: .weekOfYear, value: -7, to: endDate) ?? endDate
            let weekStartDate = calendar.startOfWeek(for: startDate)
            let weekEndDate = calendar.endOfWeek(for: endDate)
            return DateRange(startDate: weekStartDate, endDate: weekEndDate)

        case .month:
            // 8个月数据，对齐到月边界
            let startDate = calendar.date(byAdding: .month, value: -7, to: endDate) ?? endDate
            let monthStartDate = calendar.monthStart(for: startDate)
            let monthEndDate = calendar.endOfMonth(for: endDate)
            return DateRange(startDate: monthStartDate, endDate: monthEndDate)
        }
    }

    /// 清理过期缓存
    func cleanupCache() async {
        cacheManager.cleanupExpiredData()
        cacheManager.updateStatistics()

        Log.info("🧹 缓存清理完成")
    }

    /// 获取缓存统计信息
    func getCacheStatistics() -> CacheStatistics {
        cacheManager.updateStatistics()
        return cacheManager.statistics
    }
}

// MARK: - Extensions
extension ChartDataService {
    /// 获取猫咪的历史趋势数据（用于对比）
    func getHistoricalTrend(
        for catId: String,
        currentPeriod: ChartTimePeriod,
        currentRange: DateRange
    ) async -> ChartTrend {
        // 计算对比时间段（前一个相同长度的时间段）
        let duration = currentRange.duration
        let previousEndDate = currentRange.startDate
        let previousStartDate = Date(timeInterval: -duration, since: previousEndDate)
        let previousRange = DateRange(startDate: previousStartDate, endDate: previousEndDate)
        
        // 获取当前和历史数据
        let currentSegments = await fetchSegmentsFromAPI(for: catId, from: currentRange.startDate, to: currentRange.endDate)
        let previousSegments = await fetchSegmentsFromAPI(for: catId, from: previousRange.startDate, to: previousRange.endDate)
        
        let currentCount = currentSegments.count
        let previousCount = previousSegments.count
        
        // 计算趋势
        if currentCount > previousCount {
            return .increasing
        } else if currentCount < previousCount {
            return .decreasing
        } else {
            return .stable
        }
    }
}
