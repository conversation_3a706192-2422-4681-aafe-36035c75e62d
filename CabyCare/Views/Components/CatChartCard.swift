import SwiftUI

// MARK: - Cat Chart Card
struct CatChartCard: View {
    let chartData: CatChartData
    @ObservedObject var chartState: ChartViewState
    @State private var avatarRefreshTrigger = 0
    @Environment(\.colorScheme) var colorScheme
    
    private var chartConfiguration: ChartConfiguration {
        ChartConfiguration.themed(for: colorScheme)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 卡片头部 - 猫咪信息和时间选择器
            cardHeader
            
            // 分隔线
            Divider()
                .background(AppTheme.primaryColor(for: colorScheme).opacity(0.2))
            
            // 折线图内容
            chartContent
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            AppTheme.secondaryBackgroundColor(for: colorScheme),
                            AppTheme.secondaryBackgroundColor(for: colorScheme).opacity(0.95)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(
                    color: colorScheme == .dark ? Color.black.opacity(0.3) : Color.black.opacity(0.1),
                    radius: colorScheme == .dark ? 8 : 16,
                    x: 0,
                    y: colorScheme == .dark ? 2 : 6
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: [
                            AppTheme.primaryColor(for: colorScheme).opacity(0.4),
                            AppTheme.primaryColor(for: colorScheme).opacity(0.1),
                            Color.clear
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1.5
                )
        )
        .onReceive(NotificationCenter.default.publisher(for: .avatarUpdated)) { _ in
            avatarRefreshTrigger += 1
        }
    }
    
    // MARK: - Card Header
    private var cardHeader: some View {
        VStack(spacing: 16) {
            // 猫咪信息行
            HStack(spacing: 12) {
                // 猫咪头像
                CatAvatarView(
                    avatarUrl: chartData.avatarUrl,
                    size: 56,
                    cornerRadius: 28,
                    refreshTrigger: avatarRefreshTrigger
                )
                .overlay(
                    Circle()
                        .stroke(
                            LinearGradient(
                                colors: [
                                    AppTheme.primaryColor(for: colorScheme).opacity(0.6),
                                    AppTheme.primaryColor(for: colorScheme).opacity(0.2)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 2
                        )
                )
                
                // 猫咪名称和基本信息
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(chartData.catName)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))

                        Spacer()

                        // 数据类型切换按钮
                        DataTypeToggle(
                            selectedDataType: $chartState.selectedDataType,
                            colorScheme: colorScheme
                        )
                    }

                    // 根据数据类型显示不同的统计信息
                    HStack(spacing: 12) {
                        switch chartData.dataType {
                        case .toilet:
                            // 总次数
                            QuickStatView(
                                icon: "number.circle.fill",
                                value: "\(chartData.totalCount)",
                                label: "总计",
                                color: chartConfiguration.primaryColor
                            )

                            // 平均次数
                            QuickStatView(
                                icon: "chart.line.uptrend.xyaxis.circle.fill",
                                value: String(format: "%.1f", chartData.averageCount),
                                label: "平均",
                                color: chartConfiguration.secondaryColor
                            )

                        case .weight:
                            // 平均重量
                            QuickStatView(
                                icon: "scalemass.fill",
                                value: String(format: "%.1fkg", chartData.averageWeight),
                                label: "平均",
                                color: chartConfiguration.primaryColor
                            )

                            // 重量范围
                            QuickStatView(
                                icon: "arrow.up.arrow.down.circle.fill",
                                value: String(format: "%.1f-%.1fkg", chartData.minWeight, chartData.maxWeight),
                                label: "范围",
                                color: chartConfiguration.secondaryColor
                            )
                        }

                        // 趋势指示器
                        TrendIndicator(trend: chartData.trend)
                    }
                }
                
                Spacer()
            }
            
            // 时间段选择器（使用原来的样式）
            HStack(spacing: 12) {
                ForEach(ChartTimePeriod.allCases, id: \.self) { period in
                    PeriodButton(
                        period: period,
                        isSelected: chartState.selectedPeriod == period,
                        action: {
                            chartState.selectedPeriod = period
                        }
                    )
                }

                Spacer()
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    // MARK: - Chart Content
    private var chartContent: some View {
        VStack(spacing: 0) {
            if chartState.isLoading {
                // 加载状态
                loadingView
            } else if let error = chartState.error {
                // 错误状态
                errorView(error)
            } else if chartData.currentDataPoints == 0 {
                // 空数据状态
                emptyDataView
            } else {
                // 根据数据类型显示不同的图表
                switch chartData.dataType {
                case .toilet:
                    ToiletLineChart(
                        chartData: chartData,
                        configuration: chartConfiguration
                    )
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)

                case .weight:
                    WeightLineChart(
                        chartData: chartData,
                        configuration: chartConfiguration
                    )
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                }
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppTheme.primaryColor(for: colorScheme))
            
            Text("正在加载数据...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(height: 200)
    }
    
    // MARK: - Error View
    private func errorView(_ error: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 32))
                .foregroundColor(.orange)
            
            Text("数据加载失败")
                .font(.headline)
                .foregroundColor(AppTheme.textColor(for: colorScheme))
            
            Text(error)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("重试") {
                // 触发重新加载
                chartState.error = nil
                chartState.isLoading = true
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.white)
            .padding(.horizontal, 20)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppTheme.primaryColor(for: colorScheme))
            )
        }
        .frame(height: 200)
        .padding(.horizontal, 20)
    }
    
    // MARK: - Empty Data View
    private var emptyDataView: some View {
        VStack(spacing: 16) {
            Image(systemName: "chart.line.downtrend.xyaxis")
                .font(.system(size: 32))
                .foregroundColor(.secondary)
            
            Text("暂无数据")
                .font(.headline)
                .foregroundColor(AppTheme.textColor(for: colorScheme))
            
            Text("在选择的时间范围内没有找到\(chartData.catName)的如厕记录")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(height: 200)
        .padding(.horizontal, 20)
    }
}

// MARK: - Supporting Views
private struct QuickStatView: View {
    let icon: String
    let value: String
    let label: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 12))
                .foregroundColor(color)
            
            VStack(alignment: .leading, spacing: 1) {
                Text(value)
                    .font(.system(size: 14, weight: .bold, design: .rounded))
                    .foregroundColor(color)
                
                Text(label)
                    .font(.system(size: 10))
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - PeriodButton
struct PeriodButton: View {
    let period: ChartTimePeriod
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        Button(action: action) {
            Text(period.displayName)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isSelected ? .white : AppTheme.primaryColor(for: colorScheme))
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isSelected ? AppTheme.primaryColor(for: colorScheme) : Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppTheme.primaryColor(for: colorScheme), lineWidth: isSelected ? 0 : 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

private struct TrendIndicator: View {
    let trend: ChartTrend
    
    var body: some View {
        HStack(spacing: 3) {
            Image(systemName: trend.icon)
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(trend.color)
            
            Text(trend.description)
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(trend.color)
        }
        .padding(.horizontal, 6)
        .padding(.vertical, 3)
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(trend.color.opacity(0.15))
        )
    }
}

// MARK: - Data Type Toggle
struct DataTypeToggle: View {
    @Binding var selectedDataType: ChartDataType
    let colorScheme: ColorScheme

    var body: some View {
        HStack(spacing: 4) {
            ForEach(ChartDataType.allCases, id: \.self) { dataType in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        selectedDataType = dataType
                    }
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: dataType.icon)
                            .font(.system(size: 12, weight: .medium))

                        Text(dataType.displayName)
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(selectedDataType == dataType ? .white : AppTheme.textColor(for: colorScheme))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(selectedDataType == dataType ? AppTheme.primaryColor(for: colorScheme) : Color.clear)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                selectedDataType == dataType ? Color.clear : AppTheme.primaryColor(for: colorScheme).opacity(0.3),
                                lineWidth: 1
                            )
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(AppTheme.secondaryBackgroundColor(for: colorScheme).opacity(0.5))
        )
    }
}

// MARK: - Preview
struct CatChartCard_Previews: PreviewProvider {
    static var previews: some View {
        let sampleData = CatChartData(
            catId: "1",
            catName: "小黑",
            avatarUrl: nil,
            dataPoints: [
                ToiletChartDataPoint(date: Date().addingTimeInterval(-6*24*60*60), count: 3, duration: 180),
                ToiletChartDataPoint(date: Date().addingTimeInterval(-5*24*60*60), count: 2, duration: 120),
                ToiletChartDataPoint(date: Date().addingTimeInterval(-4*24*60*60), count: 4, duration: 240),
                ToiletChartDataPoint(date: Date().addingTimeInterval(-3*24*60*60), count: 3, duration: 180),
                ToiletChartDataPoint(date: Date().addingTimeInterval(-2*24*60*60), count: 5, duration: 300),
                ToiletChartDataPoint(date: Date().addingTimeInterval(-1*24*60*60), count: 2, duration: 120),
                ToiletChartDataPoint(date: Date(), count: 3, duration: 180)
            ],
            timePeriod: .day,
            dateRange: DateRange.defaultRange(for: .day)
        )
        
        CatChartCard(
            chartData: sampleData,
            chartState: ChartViewState()
        )
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
