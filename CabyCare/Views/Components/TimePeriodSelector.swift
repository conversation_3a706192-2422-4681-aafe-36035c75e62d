import SwiftUI

// MARK: - Time Period Selector
struct TimePeriodSelector: View {
    @Binding var selectedPeriod: ChartTimePeriod
    @Binding var customDateRange: DateRange?
    @State private var showingDatePicker = false
    @State private var tempStartDate = Date()
    @State private var tempEndDate = Date()
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 16) {
            // 时间段选择按钮
            HStack(spacing: 12) {
                ForEach(ChartTimePeriod.allCases, id: \.self) { period in
                    PeriodButton(
                        period: period,
                        isSelected: selectedPeriod == period && customDateRange == nil,
                        action: {
                            selectedPeriod = period
                            customDateRange = nil
                        }
                    )
                }
                
                Spacer()
                
                // 自定义日期范围按钮
                CustomDateButton(
                    isSelected: customDateRange != nil,
                    action: {
                        showingDatePicker = true
                        tempStartDate = customDateRange?.startDate ?? Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()
                        tempEndDate = customDateRange?.endDate ?? Date()
                    }
                )
            }
            
            // 当前选择的日期范围显示
            if let customRange = customDateRange {
                HStack {
                    Image(systemName: "calendar.badge.clock")
                        .foregroundColor(.blue)
                        .font(.caption)
                    
                    Text(customRange.formattedRange)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Button("重置") {
                        customDateRange = nil
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.1))
                )
            }
        }
        .sheet(isPresented: $showingDatePicker) {
            DateRangePickerView(
                startDate: $tempStartDate,
                endDate: $tempEndDate,
                onConfirm: {
                    customDateRange = DateRange(startDate: tempStartDate, endDate: tempEndDate)
                    showingDatePicker = false
                },
                onCancel: {
                    showingDatePicker = false
                }
            )
        }
    }
}

// MARK: - Period Button
private struct PeriodButton: View {
    let period: ChartTimePeriod
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: period.icon)
                    .font(.system(size: 12, weight: .medium))
                
                Text(period.displayName)
                    .font(.system(size: 14, weight: .medium))
            }
            .foregroundColor(isSelected ? .white : AppTheme.textColor(for: colorScheme))
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        isSelected
                        ? LinearGradient(
                            colors: [
                                AppTheme.primaryColor(for: colorScheme),
                                AppTheme.primaryColor(for: colorScheme).opacity(0.8)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                        : LinearGradient(
                            colors: [
                                AppTheme.secondaryBackgroundColor(for: colorScheme),
                                AppTheme.secondaryBackgroundColor(for: colorScheme).opacity(0.8)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        isSelected ? Color.clear : AppTheme.primaryColor(for: colorScheme).opacity(0.3),
                        lineWidth: 1.5
                    )
            )
            .shadow(
                color: isSelected ? AppTheme.primaryColor(for: colorScheme).opacity(0.3) : Color.clear,
                radius: isSelected ? 8 : 0,
                x: 0,
                y: isSelected ? 4 : 0
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Custom Date Button
private struct CustomDateButton: View {
    let isSelected: Bool
    let action: () -> Void
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: "calendar.badge.plus")
                    .font(.system(size: 12, weight: .medium))
                
                Text("自定义")
                    .font(.system(size: 14, weight: .medium))
            }
            .foregroundColor(isSelected ? .white : AppTheme.textColor(for: colorScheme))
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? AppTheme.primaryColor(for: colorScheme) : AppTheme.secondaryBackgroundColor(for: colorScheme))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(
                        isSelected ? Color.clear : AppTheme.primaryColor(for: colorScheme).opacity(0.3),
                        lineWidth: 1
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Date Range Picker View
struct DateRangePickerView: View {
    @Binding var startDate: Date
    @Binding var endDate: Date
    let onConfirm: () -> Void
    let onCancel: () -> Void
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // 标题
                VStack(spacing: 8) {
                    Text("选择日期范围")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("选择要查看的时间范围")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)
                
                // 日期选择器
                VStack(spacing: 20) {
                    // 开始日期
                    VStack(alignment: .leading, spacing: 8) {
                        Text("开始日期")
                            .font(.headline)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                        
                        DatePicker(
                            "",
                            selection: $startDate,
                            in: ...endDate,
                            displayedComponents: .date
                        )
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                    }
                    
                    // 结束日期
                    VStack(alignment: .leading, spacing: 8) {
                        Text("结束日期")
                            .font(.headline)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                        
                        DatePicker(
                            "",
                            selection: $endDate,
                            in: startDate...Date(),
                            displayedComponents: .date
                        )
                        .datePickerStyle(CompactDatePickerStyle())
                        .labelsHidden()
                    }
                }
                .padding(.horizontal, 20)
                
                // 预设快捷选项
                VStack(alignment: .leading, spacing: 12) {
                    Text("快捷选择")
                        .font(.headline)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 12) {
                        QuickDateButton(title: "最近7天") {
                            endDate = Date()
                            startDate = Calendar.current.date(byAdding: .day, value: -7, to: endDate) ?? endDate
                        }
                        
                        QuickDateButton(title: "最近30天") {
                            endDate = Date()
                            startDate = Calendar.current.date(byAdding: .day, value: -30, to: endDate) ?? endDate
                        }
                        
                        QuickDateButton(title: "本周") {
                            let calendar = Calendar.current
                            let today = Date()
                            startDate = calendar.dateInterval(of: .weekOfYear, for: today)?.start ?? today
                            endDate = today
                        }
                        
                        QuickDateButton(title: "本月") {
                            let calendar = Calendar.current
                            let today = Date()
                            startDate = calendar.dateInterval(of: .month, for: today)?.start ?? today
                            endDate = today
                        }
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 按钮
                HStack(spacing: 16) {
                    Button("取消") {
                        onCancel()
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppTheme.secondaryBackgroundColor(for: colorScheme))
                    )
                    
                    Button("确认") {
                        onConfirm()
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppTheme.primaryColor(for: colorScheme))
                    )
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            }
            .background(AppTheme.backgroundColor(for: colorScheme))
            .navigationBarHidden(true)
        }
    }
}

// MARK: - Quick Date Button
private struct QuickDateButton: View {
    let title: String
    let action: () -> Void
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppTheme.textColor(for: colorScheme))
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(AppTheme.secondaryBackgroundColor(for: colorScheme))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(AppTheme.primaryColor(for: colorScheme).opacity(0.3), lineWidth: 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
