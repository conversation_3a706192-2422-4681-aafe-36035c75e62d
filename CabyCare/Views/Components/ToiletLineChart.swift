import SwiftUI
import Charts

// MARK: - Toilet Line Chart
struct ToiletLineChart: View {
    let chartData: CatChartData
    let configuration: ChartConfiguration
    @State private var selectedDataPoint: ToiletChartDataPoint?
    @State private var animationProgress: Double = 0
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 16) {
            // 图表标题和统计信息
            chartHeader
            
            // 主要折线图
            mainChart
                .frame(height: 200)
            
            // 图表底部信息
            chartFooter
        }
        .onAppear {
            if configuration.animationEnabled {
                withAnimation(.easeInOut(duration: 1.0)) {
                    animationProgress = 1.0
                }
            } else {
                animationProgress = 1.0
            }
        }
    }
    
    // MARK: - Chart Header
    private var chartHeader: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("如厕次数趋势")
                    .font(.headline)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                HStack(spacing: 16) {
                    StatisticBadge(
                        title: "总计",
                        value: "\(chartData.totalCount)",
                        color: configuration.primaryColor
                    )
                    
                    StatisticBadge(
                        title: "平均",
                        value: String(format: "%.1f", chartData.averageCount),
                        color: configuration.secondaryColor
                    )
                    
                    TrendBadge(trend: chartData.trend)
                }
            }
            
            Spacer()
        }
    }
    
    // MARK: - Main Chart
    private var mainChart: some View {
        Chart {
            ForEach(Array(chartData.toiletDataPoints.enumerated()), id: \.element.id) { index, dataPoint in
                // 折线
                lineMarkForDataPoint(dataPoint)

                // 数据点
                if configuration.showDataPoints {
                    pointMarkForDataPoint(dataPoint)
                }

                // 面积填充
                areaMarkForDataPoint(dataPoint)

                // 选中点的垂直线
                if let selectedPoint = selectedDataPoint,
                   selectedPoint.id == dataPoint.id {
                    ruleMarkForDataPoint(dataPoint)
                }
            }
        }
        .chartBackground { chartProxy in
            GeometryReader { geometry in
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .onTapGesture { location in
                        handleChartTap(at: location, in: geometry, chartProxy: chartProxy)
                    }
            }
        }
        .chartXAxis {
            AxisMarks(values: .stride(by: getXAxisStride())) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 1))
                    .foregroundStyle(configuration.secondaryColor.opacity(0.3))
                
                AxisTick(stroke: StrokeStyle(lineWidth: 1))
                    .foregroundStyle(configuration.secondaryColor.opacity(0.5))
                
                AxisValueLabel {
                    if let date = value.as(Date.self) {
                        Text(formatXAxisLabel(date))
                            .font(.caption2)
                            .foregroundColor(configuration.secondaryColor)
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading) { value in
                AxisGridLine(stroke: StrokeStyle(lineWidth: 1))
                    .foregroundStyle(configuration.secondaryColor.opacity(0.3))
                
                AxisTick(stroke: StrokeStyle(lineWidth: 1))
                    .foregroundStyle(configuration.secondaryColor.opacity(0.5))
                
                AxisValueLabel {
                    if let count = value.as(Int.self) {
                        Text("\(count)")
                            .font(.caption2)
                            .foregroundColor(configuration.secondaryColor)
                    }
                }
            }
        }
        .chartYScale(domain: 0...(chartData.maxCount + 1))
        .padding(.horizontal, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(configuration.backgroundColor)
        )
    }
    
    // MARK: - Chart Footer
    private var chartFooter: some View {
        VStack(spacing: 8) {
            // 选中数据点信息
            if let selectedPoint = selectedDataPoint {
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text(selectedPoint.formattedDate)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(selectedPoint.count) 次")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(configuration.primaryColor)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("总时长")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(selectedPoint.formattedDuration)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(configuration.primaryColor.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(configuration.primaryColor.opacity(0.3), lineWidth: 1)
                        )
                )
                .transition(.opacity.combined(with: .scale))
            }
            
            // 日期范围显示
            HStack {
                Image(systemName: "calendar")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(chartData.dateRange.formattedRange)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(chartData.timePeriod.displayName)视图")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func handleChartTap(at location: CGPoint, in geometry: GeometryProxy, chartProxy: ChartProxy) {
        // 获取点击位置对应的日期
        let xPosition = location.x - geometry.frame(in: .local).minX
        
        if let date: Date = chartProxy.value(atX: xPosition) {
            // 找到最接近的数据点
            let closestPoint = chartData.toiletDataPoints.min { point1, point2 in
                abs(point1.date.timeIntervalSince(date)) < abs(point2.date.timeIntervalSince(date))
            }
            
            withAnimation(.easeInOut(duration: 0.3)) {
                selectedDataPoint = closestPoint
            }
        }
    }
    
    private func getXAxisStride() -> Calendar.Component {
        switch chartData.timePeriod {
        case .day:
            return .day
        case .week:
            return .weekOfYear
        case .month:
            return .month
        }
    }
    
    private func formatXAxisLabel(_ date: Date) -> String {
        let formatter = DateFormatter()
        
        switch chartData.timePeriod {
        case .day:
            formatter.dateFormat = "MM/dd"
        case .week:
            formatter.dateFormat = "MM/dd"
        case .month:
            formatter.dateFormat = "MM月"
        }
        
        return formatter.string(from: date)
    }

    // MARK: - Chart Mark Helpers

    private func lineMarkForDataPoint(_ dataPoint: ToiletChartDataPoint) -> some ChartContent {
        LineMark(
            x: .value("日期", dataPoint.date),
            y: .value("次数", dataPoint.count)
        )
        .foregroundStyle(
            LinearGradient(
                colors: [configuration.primaryColor, configuration.primaryColor.opacity(0.7)],
                startPoint: .leading,
                endPoint: .trailing
            )
        )
        .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
        .interpolationMethod(.catmullRom)
    }

    private func pointMarkForDataPoint(_ dataPoint: ToiletChartDataPoint) -> some ChartContent {
        PointMark(
            x: .value("日期", dataPoint.date),
            y: .value("次数", dataPoint.count)
        )
        .foregroundStyle(configuration.primaryColor)
        .symbolSize(selectedDataPoint?.id == dataPoint.id ? 100 : 64)
        .opacity(animationProgress)
    }

    private func areaMarkForDataPoint(_ dataPoint: ToiletChartDataPoint) -> some ChartContent {
        AreaMark(
            x: .value("日期", dataPoint.date),
            y: .value("次数", dataPoint.count)
        )
        .foregroundStyle(
            LinearGradient(
                colors: [
                    configuration.primaryColor.opacity(0.3),
                    configuration.primaryColor.opacity(0.1),
                    Color.clear
                ],
                startPoint: .top,
                endPoint: .bottom
            )
        )
        .opacity(animationProgress)
    }

    private func ruleMarkForDataPoint(_ dataPoint: ToiletChartDataPoint) -> some ChartContent {
        RuleMark(x: .value("日期", dataPoint.date))
            .foregroundStyle(configuration.primaryColor.opacity(0.5))
            .lineStyle(StrokeStyle(lineWidth: 2, dash: [5, 5]))
    }
}

// MARK: - Supporting Views
private struct StatisticBadge: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 2) {
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

private struct TrendBadge: View {
    let trend: ChartTrend
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: trend.icon)
                .font(.caption2)
                .foregroundColor(trend.color)
            
            Text(trend.description)
                .font(.caption2)
                .foregroundColor(trend.color)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(trend.color.opacity(0.1))
        )
    }
}
