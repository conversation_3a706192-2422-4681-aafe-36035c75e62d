import SwiftUI

struct DeveloperMenuView: View {
    @State private var showingResult = false
    @State private var resultMessage = ""
    @StateObject private var authManager = AuthManager.shared
    
    var body: some View {
        List {
            Section("用户数据") {
                Button("清除用户数据") {
                    AuthManager.shared.logout()
                    resultMessage = "已清除所有用户数据"
                    showingResult = true
                }
            }
            
            Section("调试工具") {
                Button(action: {
                    Log.exportLogFiles().forEach { url in
                        print("📄 导出的日志文件路径: \(url.path)")
                    }
                    resultMessage = "日志文件已导出"
                    showingResult = true
                }) {
                    Label("导出日志文件", systemImage: "doc.text")
                }
            }
        }
        .navigationTitle("开发者菜单")
        .alert("操作结果", isPresented: $showingResult) {
            But<PERSON>("确定", role: .cancel) {}
        } message: {
            Text(resultMessage)
        }
    }
}
