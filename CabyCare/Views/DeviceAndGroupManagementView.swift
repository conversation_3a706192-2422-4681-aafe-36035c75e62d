import SwiftUI
import Foundation

struct DeviceAndGroupManagementView: View {
    @StateObject private var manager = DeviceAndGroupManager()
    @StateObject private var otaStatusManager = DeviceOTAStatusManager.shared
    @Environment(\.colorScheme) var colorScheme
    @State private var selectedTab: Int = 0
    @State private var currentDevice: DeviceStatusResponse?
    @State private var currentGroup: FamilyGroup?
    @State private var showCreateInvitation = false
    @State private var showCreateGroup = false
    @State private var showAddDevice = false
    @State private var showDeviceDetail = false
    @State private var showGroupDetail = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部标签页选择器
                Picker(NSLocalizedString("device_group_picker_label", comment: "选择视图"), selection: $selectedTab) {
                    Text(NSLocalizedString("device_management_tab", comment: "设备管理")).tag(0)
                    Text(NSLocalizedString("family_group_tab", comment: "家庭组")).tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding(.horizontal, 20)
                .padding(.bottom, 16)
                .background(Color(.systemGroupedBackground))
                
                // 内容视图
                contentView
            }
            .navigationTitle(NSLocalizedString("device_and_group_title", comment: "设备与家庭组"))
            .navigationBarTitleDisplayMode(.large)
            .background(Color(.systemGroupedBackground))
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 12) {
                        // 设置按钮
                        NavigationLink(destination: NotificationSettingsView()) {
                            Image(systemName: "gear")
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                        }

                        // 添加菜单
                        Menu {
                            // 设备相关选项
                            Button {
                                showAddDevice = true
                            } label: {
                                Label(NSLocalizedString("add_device", comment: "添加设备"), systemImage: "plus.circle")
                            }

                            Divider()

                            // 家庭组相关选项
                            Button {
                                showCreateGroup = true
                            } label: {
                                Label(NSLocalizedString("create_new_group", comment: "创建新家庭组"), systemImage: "house.circle")
                            }

                            Button {
                                showCreateInvitation = true
                            } label: {
                                Label(NSLocalizedString("invite_user_to_group", comment: "邀请用户加入家庭组"), systemImage: "person.badge.plus")
                            }
                        } label: {
                            Image(systemName: "plus")
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                        }
                    }
                }
            }
            .task {
                await loadInitialData()
            }
            .onChange(of: manager.devices) { oldDevices, newDevices in
                // 当设备列表变化时，更新OTA状态监控
                let deviceIds = newDevices.map { $0.id }
                otaStatusManager.startMonitoring(deviceIds: deviceIds)
            }
            .onDisappear {
                // 页面消失时停止OTA状态监控
                otaStatusManager.stopAllMonitoring()
            }
            .sheet(isPresented: $showCreateInvitation) {
                CreateInvitationView(groups: manager.familyGroups)
            }
            .sheet(isPresented: $showCreateGroup) {
                CreateFamilyGroupView(manager: manager)
            }
            .sheet(isPresented: $showAddDevice) {
                AddDeviceView()
            }
            .sheet(isPresented: $showDeviceDetail) {
                if let device = currentDevice {
                    NavigationView {
                        DeviceDetailEditView(device: device)
                            .environmentObject(manager)
                    }
                }
            }
            .sheet(isPresented: $showGroupDetail) {
                if let group = currentGroup {
                    NavigationView {
                        GroupDetailEditView(group: group)
                    }
                }
            }
        }
        .environmentObject(manager)
    }
    
    private var contentView: some View {
        Group {
            if selectedTab == 0 {
                DeviceListView(
                    currentDevice: $currentDevice,
                    devices: manager.devices,
                    isLoading: manager.isFetchingDevices,
                    isAutoRefreshing: manager.isAutoRefreshing,
                    onRefreshDeviceStatus: { deviceId in
                        await manager.updateDeviceStatus(deviceId: deviceId)
                    },
                    onDeviceTap: { device in
                        // 直接进入设备详情页面
                        currentDevice = device
                        showDeviceDetail = true
                    }
                )
            } else {
                FamilyGroupListView(
                    currentGroup: $currentGroup,
                    manager: manager,
                    onGroupTap: { group in
                        // 直接进入家庭组详情页面
                        currentGroup = group
                        showGroupDetail = true
                    }
                )
            }
        }
    }
    

    
    private func loadInitialData() async {
        // 优先显示缓存的设备 - 不需要等待网络请求
        // manager已经在init()中加载了缓存，这里只需要进行后台刷新
        
        // 并行加载设备和家庭组数据
        await withTaskGroup(of: Void.self) { group in
            group.addTask {
                // 在后台刷新设备数据，不阻塞UI显示
                await manager.fetchAccessibleDevices(forceRefresh: false)
            }
            
            group.addTask {
                await manager.fetchFamilyGroups()
            }
            
            group.addTask {
                await manager.fetchInvitations()
            }
        }
        
        // 如果有设备但没有选中设备，自动选择第一个
        if currentDevice == nil, let firstDevice = manager.devices.first {
            await MainActor.run {
                currentDevice = firstDevice
            }
        }
        
        // 如果有家庭组但没有选中组，自动选择第一个
        if currentGroup == nil, let firstGroup = manager.familyGroups.first {
            await MainActor.run {
                currentGroup = firstGroup
            }
        }
    }
    
    // MARK: - Helper Methods
    
    private func roleName(for role: FamilyGroupRole) -> String {
        switch role {
        case .owner: return NSLocalizedString("group_role_owner", comment: "拥有者")
        case .admin: return NSLocalizedString("group_role_admin", comment: "管理员")
        case .member: return NSLocalizedString("group_role_member", comment: "成员")
        }
    }
    
    private func roleColor(for role: FamilyGroupRole) -> Color {
        switch role {
        case .owner: return .red
        case .admin: return .orange
        case .member: return AppTheme.primaryColor(for: colorScheme)
        }
    }
}

// MARK: - FamilyGroupRow Component

/// 家庭组行视图
struct FamilyGroupRow: View {
    let group: FamilyGroup
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        HStack(spacing: 16) {
            // 家庭组图标
            RoundedRectangle(cornerRadius: 12)
                .fill(AppTheme.primaryColor(for: colorScheme).opacity(0.1))
                .frame(width: 48, height: 48)
                .overlay(
                    Image(systemName: "person.2.fill")
                        .font(.title3)
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                )
            
            // 家庭组信息
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(group.name)
                        .font(.headline)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                    
                    Spacer()
                    
                    Text(roleName(for: group.role))
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(roleColor(for: group.role).opacity(0.1))
                        )
                        .foregroundColor(roleColor(for: group.role))
                }
                
                HStack(spacing: 12) {
                    HStack(spacing: 4) {
                        Image(systemName: "person.fill")
                            .font(.caption2)
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                        Text(String(format: NSLocalizedString("group_member_count", comment: "%d 位成员"), group.memberCount))
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                    
                    HStack(spacing: 4) {
                        Image(systemName: "video.fill")
                            .font(.caption2)
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                        Text(String(format: NSLocalizedString("group_device_count", comment: "%d 台设备"), group.deviceCount))
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                }
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
    }
    
    private func roleName(for role: FamilyGroupRole) -> String {
        switch role {
        case .owner: return "拥有者"
        case .admin: return "管理员"
        case .member: return "成员"
        }
    }
    
    private func roleColor(for role: FamilyGroupRole) -> Color {
        switch role {
        case .owner: return .red
        case .admin: return .orange
        case .member: return AppTheme.primaryColor(for: colorScheme)
        }
    }
}

// MARK: - DeviceDetailView Component

/// 设备详情视图
struct DeviceDetailView: View {
    let device: DeviceViewModel
    @Environment(\.colorScheme) var colorScheme
    @Environment(\.presentationMode) var presentationMode
    @State private var showAddToGroupSheet = false
    @State private var selectedGroupId: String = ""
    @State private var groups: [FamilyGroup] = []
    @State private var isLoading = false
    @State private var isFetchingGroups = false
    @State private var errorMessage: String? = nil
    @State private var showSuccess = false
    @State private var isRefreshingStatus = false
    @State private var deviceStatus: DeviceStatusResponse? = nil
    @State private var isRefreshingSensorStatus = false
    @EnvironmentObject private var deviceManager: DeviceAndGroupManager
    
    var body: some View {
        List {
            Section(header: Text("设备信息")) {
                DetailRow(title: "设备名称", value: device.name)
                DetailRow(title: "设备型号", value: device.model)
                DetailRow(title: "固件版本", value: device.firmwareVersion)
                DetailRow(title: "时区", value: device.timezone)
                
                // 显示基础状态
                HStack {
                    Text("基础状态")
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    Spacer()
                    Text(device.status == .online ? "在线" : "离线")
                        .fontWeight(.medium)
                        .foregroundColor(device.status == .online ? .green : .red)
                }
                
                if let lastActive = device.lastActive {
                    DetailRow(title: "最后活动", value: lastActive)
                }
            }
            
            // 实时状态部分
            Section(header: HStack {
                Text("实时状态")
                Spacer()
                Button("刷新状态") {
                    refreshDeviceStatus()
                }
                .font(.caption)
                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                .disabled(isRefreshingStatus)
            }) {
                if isRefreshingStatus {
                    HStack {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("获取状态中...")
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            .padding(.leading, 8)
                        Spacer()
                    }
                    .padding(.vertical, 8)
                } else if let status = deviceStatus {
                    DetailRow(title: "设备名称", value: status.name)
                    DetailRow(title: "设备型号", value: status.model)
                    DetailRow(title: "固件版本", value: status.firmware)
                    
                    HStack {
                        Text("在线状态")
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        Spacer()
                        HStack {
                            Circle()
                                .fill(status.online == true ? Color.green : status.online == false ? Color.red : Color.gray)
                                .frame(width: 8, height: 8)
                            Text(status.online == true ? "在线" : status.online == false ? "离线" : "未知")
                                .fontWeight(.medium)
                                .foregroundColor(status.online == true ? .green : status.online == false ? .red : .gray)
                        }
                    }
                    
                    if let lastHeartbeat = status.lastHeartbeat {
                        DetailRow(title: "最后心跳", value: lastHeartbeat)
                    }
                    
                    if let ipv4 = status.ipv4 {
                        DetailRow(title: "IPv4地址", value: ipv4)
                    }
                    
                    if let ipv6 = status.ipv6 {
                        DetailRow(title: "IPv6地址", value: ipv6)
                    }
                } else {
                    Text("点击刷新状态获取实时设备状态")
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.vertical, 8)
                }
            }
            
            // 🔄 新增：传感器状态部分
            Section(header: HStack {
                Text("传感器状态")
                Spacer()
                Button("刷新传感器") {
                    refreshSensorStatus()
                }
                .font(.caption)
                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                .disabled(isRefreshingSensorStatus)
            }) {
                SensorStatusDetailView(deviceId: device.id)
            }
            
            Section(header: Text("添加到家庭组")) {
                if isFetchingGroups {
                    HStack {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                        Text("获取家庭组...")
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            .padding(.leading, 8)
                        Spacer()
                    }
                    .padding(.vertical, 8)
                } else if groups.isEmpty {
                    Text("暂无家庭组，请先创建家庭组")
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding(.vertical, 8)
                } else {
                    Button("添加到家庭组") {
                        showAddToGroupSheet = true
                    }
                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                }
                
                if let error = errorMessage {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.footnote)
                }
            }
        }
        .navigationTitle(device.name)
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $showAddToGroupSheet) {
            AddDeviceToGroupSheet(
                device: device,
                groups: groups,
                selectedGroupId: $selectedGroupId,
                isLoading: $isLoading,
                errorMessage: $errorMessage,
                showSuccess: $showSuccess
            )
        }
        .alert(isPresented: $showSuccess) {
            Alert(
                title: Text("添加成功"),
                message: Text("设备已成功添加到家庭组"),
                dismissButton: .default(Text("确定"))
            )
        }
        .onAppear {
            fetchGroups()
            
            // 获取传感器状态
            Task {
                await deviceManager.fetchSensorStatus(for: device.id)
            }
        }
    }
    
    // 刷新设备状态
    private func refreshDeviceStatus() {
        isRefreshingStatus = true
        
        Task {
            // 模拟网络请求
            try? await Task.sleep(nanoseconds: 1_000_000_000)
            
            // TODO: 这里应该调用实际的API获取设备状态
            await MainActor.run {
                // 这里应该更新deviceStatus，目前先模拟一下
                deviceStatus = DeviceStatusResponse(
                    id: device.id,
                    name: device.name,
                    model: device.model,
                    firmware: device.firmwareVersion,
                    online: device.status == .online
                )
                isRefreshingStatus = false
            }
        }
    }
    
    // 刷新传感器状态
    private func refreshSensorStatus() {
        isRefreshingSensorStatus = true
        
        Task {
            await deviceManager.fetchSensorStatus(for: device.id)
            await MainActor.run {
                isRefreshingSensorStatus = false
            }
        }
    }
    
    // 获取用户的家庭组列表
    private func fetchGroups() {
        guard UserDefaultsManager.shared.userId != nil else {
            errorMessage = "无法获取用户ID"
            return
        }
        
        isFetchingGroups = true
        
        Task {
            do {
                // 使用DeviceAndGroupManager获取家庭组列表
                let manager = DeviceAndGroupManager()
                let fetchedGroups = try await manager.fetchFamilyGroupMembers(groupId: "")  // 传空字符串获取所有组
                
                await MainActor.run {
                    self.groups = fetchedGroups
                    self.isFetchingGroups = false
                }
                
            } catch {
                Log.error("❌ 获取家庭组失败: \(error.localizedDescription)")
                
                await MainActor.run {
                    self.errorMessage = "获取家庭组失败: \(error.localizedDescription)"
                    self.isFetchingGroups = false
                }
            }
        }
    }
    
    // 添加设备到选定的家庭组
    private func addDeviceToGroup() {
        guard !selectedGroupId.isEmpty else {
            errorMessage = "请选择家庭组"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            let manager = DeviceAndGroupManager()
            do {
                try await manager.addDeviceToGroup(deviceId: device.id, groupId: selectedGroupId)
                await MainActor.run {
                    self.showSuccess = true
                    self.isLoading = false
                    self.presentationMode.wrappedValue.dismiss()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "添加失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
}

/// 详情行视图
struct DetailRow: View {
    let title: String
    let value: String
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        HStack {
            Text(title)
                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            Spacer()
            Text(value)
                .fontWeight(.medium)
                .foregroundColor(AppTheme.textColor(for: colorScheme))
        }
    }
}

struct DeviceAndGroupManagementView_Previews: PreviewProvider {
    static var previews: some View {
        DeviceAndGroupManagementView()
            .previewDisplayName("主视图")
    }
}

// MARK: - DeviceDetailEditView

/// 设备详情编辑视图
struct DeviceDetailEditView: View {
    let device: DeviceStatusResponse
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.colorScheme) var colorScheme
    @State private var deviceName: String
    @State private var deviceLocation: String
    @State private var isEditing = false
    @State private var isSaving = false
    @State private var errorMessage: String? = nil
    @State private var showSuccess = false
    
    // OTA设置相关状态
    @State private var otaSettings: DeviceOTASettings? = nil
    @State private var isLoadingOTA = false
    @State private var isSavingOTA = false
    @State private var otaErrorMessage: String? = nil
    @State private var showOTASuccess = false
    @State private var isEditingOTA = false
    
    // OTA编辑状态
    @State private var autoOtaEnabled = false
    @State private var startTime = Date()
    @State private var endTime = Date()
    @State private var betaUpdates = false
    
    // 🆕 新增：闲时更新时间设置
    @State private var updateMode: OTAUpdateMode = .idle
    @State private var idleUpdateStartHour: Int = 2
    @State private var idleUpdateEndHour: Int = 4
    
    // 🔄 新增：传感器状态相关状态
    @State private var isRefreshingSensorStatus = false
    @EnvironmentObject private var deviceManager: DeviceAndGroupManager
    
    init(device: DeviceStatusResponse) {
        self.device = device
        self._deviceName = State(initialValue: device.name)
        self._deviceLocation = State(initialValue: device.location)
    }
    
    var body: some View {
        List {
            Section(header: Text("设备信息")) {
                HStack {
                    Text("设备名称")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    if isEditing {
                        TextField("设备名称", text: $deviceName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    } else {
                        Text(deviceName)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                    }
                }
                
                HStack {
                    Text("设备位置")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    if isEditing {
                        TextField("设备位置", text: $deviceLocation)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    } else {
                        Text(deviceLocation)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                    }
                }
                
                HStack {
                    Text("设备型号")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    Text(device.model)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
                
                HStack {
                    Text("固件版本")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    Text("v\(device.firmware)")
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
                
                HStack {
                    Text("设备状态")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    HStack(spacing: 4) {
                        Circle()
                            .fill(device.online == true ? Color.green : device.online == false ? Color.red : Color.gray)
                            .frame(width: 8, height: 8)
                        Text(device.online == true ? "在线" : device.online == false ? "离线" : "未知")
                            .foregroundColor(device.online == true ? .green : device.online == false ? .red : .gray)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }
            }
            
            // OTA状态部分
            Section {
                DetailedOTAStatusView(deviceId: device.id)
            }
            
            // 🔄 新增：传感器状态部分
            Section(header: HStack {
                Text("传感器状态")
                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                Spacer()
                Button("刷新传感器") {
                    refreshSensorStatus()
                }
                .font(.caption)
                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                .disabled(isRefreshingSensorStatus)
            }) {
                SensorStatusDetailView(deviceId: device.id)
            }

            if let error = errorMessage {
                Section {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.footnote)
                }
            }
            
            // OTA设置部分
            Section(header: HStack {
                Text("OTA升级设置")
                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                Spacer()
                if isLoadingOTA {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Button("刷新") {
                        loadOTASettings()
                    }
                    .font(.caption)
                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                }
            }) {
                if let ota = otaSettings {
                    // 自动升级开关
                    HStack {
                        Text("自动升级")
            
                        Spacer()
                        if isEditingOTA {
                            Toggle("", isOn: $autoOtaEnabled)
                                .labelsHidden()
                        } else {
                            Text(ota.autoOtaEnabled ? "已开启" : "已关闭")
                                .foregroundColor(ota.autoOtaEnabled ? .green : AppTheme.secondaryTextColor(for: colorScheme))
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                    }
                    
                    // 🆕 当自动升级开启且在编辑状态时，显示更新时间设置
                    if isEditingOTA && autoOtaEnabled {
                        VStack(alignment: .leading, spacing: 16) {
                            // 更新模式选择
                            VStack(alignment: .leading, spacing: 8) {
                                Text("更新时间设置")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                                
                                Picker("更新模式", selection: $updateMode) {
                                    Text("立即更新").tag(OTAUpdateMode.immediate)
                                    Text("闲时更新").tag(OTAUpdateMode.idle)
                                }
                                .pickerStyle(SegmentedPickerStyle())
                                
                                // 模式说明
                                Text(updateMode == .immediate ? "选择立即更新将在设备空闲时尽快开始升级" : "选择闲时更新将在指定时间范围内进行升级")
                                    .font(.caption)
                                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            }
                            
                            // 闲时更新时间选择器（仅在闲时模式下显示）
                            if updateMode == .idle {
                                VStack(alignment: .leading, spacing: 12) {
                                    Text("闲时更新时间范围")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                                    
                                    HStack(spacing: 16) {
                                        // 开始时间选择
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text("开始时间")
                                                .font(.caption)
                                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                            
                                            Picker("开始时间", selection: $idleUpdateStartHour) {
                                                ForEach(0..<24, id: \.self) { hour in
                                                    Text(String(format: "%02d:00", hour)).tag(hour)
                                                }
                                            }
                                            .pickerStyle(WheelPickerStyle())
                                            .frame(height: 80)
                                            .clipped()
                                        }
                                        
                                        // 分隔符
                                        Text("至")
                                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                            .padding(.top, 20)
                                        
                                        // 结束时间选择
                                        VStack(alignment: .leading, spacing: 4) {
                                            Text("结束时间")
                                                .font(.caption)
                                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                            
                                            Picker("结束时间", selection: $idleUpdateEndHour) {
                                                ForEach(0..<24, id: \.self) { hour in
                                                    Text(String(format: "%02d:00", hour)).tag(hour)
                                                }
                                            }
                                            .pickerStyle(WheelPickerStyle())
                                            .frame(height: 80)
                                            .clipped()
                                        }
                                    }
                                    
                                    // 时间范围验证提示
                                    if idleUpdateStartHour == idleUpdateEndHour {
                                        HStack {
                                            Image(systemName: "info.circle")
                                                .foregroundColor(.orange)
                                            Text("开始和结束时间相同，等同于立即更新模式")
                                                .font(.caption)
                                                .foregroundColor(.orange)
                                        }
                                    } else if (idleUpdateEndHour <= idleUpdateStartHour) {
                                        HStack {
                                            Image(systemName: "info.circle")
                                                .foregroundColor(.blue)
                                            Text("跨日更新：从\(String(format: "%02d:00", idleUpdateStartHour))到次日\(String(format: "%02d:00", idleUpdateEndHour))")
                                                .font(.caption)
                                                .foregroundColor(.blue)
                                        }
                                    } else {
                                        HStack {
                                            Image(systemName: "checkmark.circle")
                                                .foregroundColor(.green)
                                            Text("更新时间：\(String(format: "%02d:00", idleUpdateStartHour))-\(String(format: "%02d:00", idleUpdateEndHour))")
                                                .font(.caption)
                                                .foregroundColor(.green)
                                        }
                                    }
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(AppTheme.primaryColor(for: colorScheme).opacity(0.05))
                                        .stroke(AppTheme.primaryColor(for: colorScheme).opacity(0.2), lineWidth: 1)
                                )
                            }
                        }
                        .padding(.top, 8)
                    }
                    
                    // 🆕 当不在编辑状态时，显示当前的闲时更新设置
                    if !isEditingOTA && ota.autoOtaEnabled {
                        if let startHour = ota.idleUpdateStartHour, let endHour = ota.idleUpdateEndHour {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("闲时更新设置")
                                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                                    Spacer()
                                }
                                
                                HStack {
                                    Image(systemName: startHour == endHour ? "bolt.circle" : "clock")
                                        .font(.caption)
                                        .foregroundColor(startHour == endHour ? .orange : AppTheme.primaryColor(for: colorScheme))
                                    
                                    if startHour == endHour {
                                        Text("立即更新模式")
                                            .font(.caption)
                                            .foregroundColor(.orange)
                                    } else {
                                        Text("闲时更新: \(String(format: "%02d:00", startHour)) - \(String(format: "%02d:00", endHour))")
                                            .font(.caption)
                                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                    }
                                    Spacer()
                                }
                            }
                        }
                    }
                    
                    // 升级时间窗口（仅在标准格式且非编辑状态下显示）
                    if !isEditingOTA && ota.otaTimeWindow.start != "02:00" || ota.otaTimeWindow.end != "04:00" {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("升级时间窗口")
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                                Spacer()
                            }
                            
                            HStack {
                                Image(systemName: "clock")
                                    .font(.caption)
                                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                                Text("\(ota.otaTimeWindow.start) - \(ota.otaTimeWindow.end)")
                                    .font(.caption)
                                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                Spacer()
                            }
                        }
                    }
                    
                    // Beta更新（仅在编辑状态或有实际值时显示）
                    if isEditingOTA || ota.betaUpdates {
                        HStack {
                            Text("Beta更新")
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            Spacer()
                            if isEditingOTA {
                                Toggle("", isOn: $betaUpdates)
                                    .labelsHidden()
                                    .disabled(true) // 暂时禁用，因为API不支持
                                    .opacity(0.5)
                            } else {
                                Text(ota.betaUpdates ? "已开启" : "已关闭")
                                    .foregroundColor(ota.betaUpdates ? .orange : AppTheme.secondaryTextColor(for: colorScheme))
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                        }
                    }
                    
                    // 检查时间信息（仅在有值时显示）
                    if let lastCheck = ota.lastCheck {
                        HStack {
                            Text("上次检查")
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            Spacer()
                            Text(formatOTATime(lastCheck))
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        }
                    }
                    
                    if let nextCheck = ota.nextCheck {
                        HStack {
                            Text("下次检查")
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            Spacer()
                            Text(formatOTATime(nextCheck))
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        }
                    }
                    
                    // 如果是简化格式，显示提示信息
                    if ota.lastCheck == nil && ota.nextCheck == nil && 
                       ota.otaTimeWindow.start == "02:00" && ota.otaTimeWindow.end == "04:00" {
                        HStack {
                            Image(systemName: "info.circle")
                                .font(.caption)
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            Text("当前使用简化模式，仅支持开关控制")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            Spacer()
                        }
                        .padding(.top, 4)
                    }
                    
                } else if isLoadingOTA {
                    HStack {
                        Spacer()
                        ProgressView("正在加载OTA设置...")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        Spacer()
                    }
                    .padding(.vertical, 8)
                } else {
                    HStack {
                        Spacer()
                        VStack(spacing: 4) {
                            Image(systemName: "arrow.down.circle")
                                .font(.title3)
                                .foregroundColor(AppTheme.primaryColor(for: colorScheme).opacity(0.6))
                            Text("点击刷新加载OTA设置")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        }
                        Spacer()
                    }
                    .padding(.vertical, 8)
                }
                
                if let error = otaErrorMessage {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.footnote)
                }
            }
        }
        .navigationTitle("设备详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                if isEditing || isEditingOTA {
                    HStack {
                        Button("取消") {
                            if isEditing {
                                isEditing = false
                                deviceName = device.name
                                deviceLocation = device.location
                                errorMessage = nil
                            }
                            if isEditingOTA {
                                isEditingOTA = false
                                resetOTAEditingState()
                                otaErrorMessage = nil
                            }
                        }
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        
                        Button("保存") {
                            if isEditing {
                                saveChanges()
                            }
                            if isEditingOTA {
                                saveOTASettings()
                            }
                        }
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                        .disabled(isSaving || isSavingOTA)
                    }
                } else {
                    Menu {
                        Button("编辑设备信息") {
                            isEditing = true
                        }
                        
                        if otaSettings != nil {
                            Button("编辑OTA设置") {
                                isEditingOTA = true
                                setupOTAEditingState()
                            }
                        }
                    } label: {
                        Text("编辑")
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    }
                }
            }
        }
        .alert(isPresented: $showSuccess) {
            Alert(
                title: Text("保存成功"),
                message: Text("设备信息已更新"),
                dismissButton: .default(Text("确定")) {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
        .alert("OTA设置保存成功", isPresented: $showOTASuccess) {
            Button("确定") {
                // 重新加载OTA设置
                loadOTASettings()
            }
        }
        .onAppear {
            // 页面出现时自动加载OTA设置
            loadOTASettings()
            
            // 🔄 新增：获取传感器状态
            Task {
                await deviceManager.fetchSensorStatus(for: device.id)
            }
        }
    }
    
    // 🔄 新增：刷新传感器状态方法
    private func refreshSensorStatus() {
        isRefreshingSensorStatus = true
        
        Task {
            await deviceManager.fetchSensorStatus(for: device.id)
            await MainActor.run {
                isRefreshingSensorStatus = false
            }
        }
    }
    
    private func saveChanges() {
        isSaving = true
        errorMessage = nil

        Task {
            do {
                // 使用新的设备基本信息更新API
                try await deviceManager.updateDeviceBasicInfo(
                    deviceId: device.id,
                    name: deviceName != device.name ? deviceName : nil
                )

                await MainActor.run {
                    isSaving = false
                    isEditing = false
                    showSuccess = true
                }

                Log.info("✅ 设备信息保存成功: \(deviceName)")

            } catch {
                Log.error("❌ 设备信息保存失败: \(error.localizedDescription)")

                await MainActor.run {
                    isSaving = false
                    errorMessage = "保存失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func loadOTASettings() {
        isLoadingOTA = true
        errorMessage = nil
        
        Task {
            let manager = DeviceAndGroupManager()
            let settings = await manager.fetchDeviceOTASettings(deviceId: device.id)
            
            await MainActor.run {
                self.otaSettings = settings
                self.isLoadingOTA = false
            }
        }
    }
    
    private func formatOTATime(_ time: String) -> String {
        // 如果是ISO时间格式，转换为本地时间显示
        if let date = ISO8601DateFormatter().date(from: time) {
            let formatter = DateFormatter()
            formatter.dateStyle = .short
            formatter.timeStyle = .short
            return formatter.string(from: date)
        }
        // 如果是HH:mm格式，直接返回
        return time
    }
    
    private func resetOTAEditingState() {
        autoOtaEnabled = false
        betaUpdates = false
        updateMode = .idle
        idleUpdateStartHour = 2
        idleUpdateEndHour = 4
    }
    
    private func setupOTAEditingState() {
        if let ota = otaSettings {
            autoOtaEnabled = ota.autoOtaEnabled
            betaUpdates = ota.betaUpdates
            
            // 🆕 设置闲时更新时间相关状态
            if let startHour = ota.idleUpdateStartHour, let endHour = ota.idleUpdateEndHour {
                idleUpdateStartHour = startHour
                idleUpdateEndHour = endHour
                
                // 根据时间设置判断更新模式
                if startHour == endHour {
                    updateMode = .immediate
                } else {
                    updateMode = .idle
                }
            } else {
                // 默认值
                idleUpdateStartHour = 2
                idleUpdateEndHour = 4
                updateMode = .idle
            }
        }
    }
    
    private func saveOTASettings() {
        isSavingOTA = true
        otaErrorMessage = nil
        
        Task {
            do {
                let manager = DeviceAndGroupManager()
                
                // 🆕 根据更新模式计算实际的开始和结束时间
                let actualStartHour: Int
                let actualEndHour: Int
                
                if !autoOtaEnabled {
                    // 如果自动升级关闭，时间参数设为nil
                    actualStartHour = 2 // 默认值
                    actualEndHour = 4   // 默认值
                } else if updateMode == .immediate {
                    // 立即更新模式：开始和结束时间相同
                    let currentHour = Calendar.current.component(.hour, from: Date())
                    actualStartHour = currentHour
                    actualEndHour = currentHour
                } else {
                    // 闲时更新模式：使用用户选择的时间
                    actualStartHour = idleUpdateStartHour
                    actualEndHour = idleUpdateEndHour
                }
                
                let request = DeviceOTAUpdateRequest(
                    autoOtaEnabled: autoOtaEnabled,
                    otaTimeWindow: OTATimeWindow.defaultWindow,
                    betaUpdates: false,
                    idleUpdateStartHour: autoOtaEnabled ? actualStartHour : nil,
                    idleUpdateEndHour: autoOtaEnabled ? actualEndHour : nil
                )
                
                try await manager.updateDeviceOTASettings(deviceId: device.id, request: request)
                
                await MainActor.run {
                    isSavingOTA = false
                    isEditingOTA = false
                    showOTASuccess = true
                }
            } catch {
                Log.error("❌ 保存OTA设置失败: \(error.localizedDescription)")
                
                await MainActor.run {
                    isSavingOTA = false
                    otaErrorMessage = "保存OTA设置失败: \(error.localizedDescription)"
                }
            }
        }
    }
}

// 🆕 OTA更新模式枚举
enum OTAUpdateMode: CaseIterable {
    case immediate  // 立即更新
    case idle      // 闲时更新
    
    var displayName: String {
        switch self {
        case .immediate:
            return "立即更新"
        case .idle:
            return "闲时更新"
        }
    }
}

// MARK: - GroupDetailEditView

/// 家庭组详情编辑视图
struct GroupDetailEditView: View {
    let group: FamilyGroup
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.colorScheme) var colorScheme
    @State private var groupName: String
    @State private var groupDescription: String
    @State private var isEditing = false
    @State private var isSaving = false
    @State private var errorMessage: String? = nil
    @State private var showSuccess = false
    
    init(group: FamilyGroup) {
        self.group = group
        self._groupName = State(initialValue: group.name)
        self._groupDescription = State(initialValue: group.description)
    }
    
    var body: some View {
        List {
            Section(header: Text("家庭组信息")) {
                HStack {
                    Text("组名称")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    if isEditing {
                        TextField("家庭组名称", text: $groupName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                    } else {
                        Text(groupName)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                    }
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("组描述")
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                        Spacer()
                    }
                    
                    if isEditing {
                        TextEditor(text: $groupDescription)
                            .frame(minHeight: 60)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    } else {
                        Text(groupDescription.isEmpty ? "暂无描述" : groupDescription)
                            .foregroundColor(groupDescription.isEmpty ? AppTheme.secondaryTextColor(for: colorScheme) : AppTheme.textColor(for: colorScheme))
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                
                HStack {
                    Text("我的角色")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    Text(roleName(for: group.role))
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(roleColor(for: group.role))
                        )
                }
                
                HStack {
                    Text("成员数量")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    Text("\(group.memberCount) 位成员")
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
                
                HStack {
                    Text("设备数量")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Spacer()
                    Text("\(group.deviceCount) 台设备")
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
            }
            
            if let error = errorMessage {
                Section {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.footnote)
                }
            }
        }
        .navigationTitle("家庭组详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("关闭") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                if group.role == .owner || group.role == .admin {
                    if isEditing {
                        HStack {
                            Button("取消") {
                                isEditing = false
                                groupName = group.name
                                groupDescription = group.description
                                errorMessage = nil
                            }
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            
                            Button("保存") {
                                saveChanges()
                            }
                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                            .disabled(isSaving)
                        }
                    } else {
                        Button("编辑") {
                            isEditing = true
                        }
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    }
                }
            }
        }
        .alert(isPresented: $showSuccess) {
            Alert(
                title: Text("保存成功"),
                message: Text("家庭组信息已更新"),
                dismissButton: .default(Text("确定")) {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
    
    private func saveChanges() {
        isSaving = true
        errorMessage = nil
        
        // TODO: 调用API保存家庭组信息
        // 这里预留API调用接口
        Task {
            do {
                // 模拟API调用
                try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
                
                await MainActor.run {
                    isSaving = false
                    isEditing = false
                    showSuccess = true
                }
            } catch {
                await MainActor.run {
                    isSaving = false
                    errorMessage = "保存失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func roleName(for role: FamilyGroupRole) -> String {
        switch role {
        case .owner: return "拥有者"
        case .admin: return "管理员"
        case .member: return "成员"
        }
    }
    
    private func roleColor(for role: FamilyGroupRole) -> Color {
        switch role {
        case .owner: return .red
        case .admin: return .orange
        case .member: return AppTheme.primaryColor(for: colorScheme)
        }
    }
}

// MARK: - OTA Settings Models

/// OTA时间窗口配置
struct OTATimeWindow: Codable {
    let start: String // 格式: "HH:mm"
    let end: String   // 格式: "HH:mm"
    
    // 提供默认值
    static let defaultWindow = OTATimeWindow(start: "02:00", end: "04:00")
}

/// 设备OTA设置
struct DeviceOTASettings: Codable {
    let deviceId: String
    let autoOtaEnabled: Bool
    let otaTimeWindow: OTATimeWindow
    let betaUpdates: Bool
    let lastCheck: String?
    let nextCheck: String?
    
    // 🆕 新增：闲时更新时间
    let idleUpdateStartHour: Int?
    let idleUpdateEndHour: Int?
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case autoOtaEnabled = "auto_ota_enabled"
        case otaTimeWindow = "ota_time_window"
        case betaUpdates = "beta_updates"
        case lastCheck = "last_check"
        case nextCheck = "next_check"
        case idleUpdateStartHour = "idle_update_start_hour"
        case idleUpdateEndHour = "idle_update_end_hour"
    }
    
    // 添加从简单格式创建的初始化方法
    init(from simpleOTA: SimpleOTASettings, deviceId: String) {
        self.deviceId = deviceId
        self.autoOtaEnabled = simpleOTA.autoOtaUpgrade == "on"
        self.otaTimeWindow = OTATimeWindow.defaultWindow
        self.betaUpdates = false
        self.lastCheck = nil
        self.nextCheck = nil
        self.idleUpdateStartHour = nil
        self.idleUpdateEndHour = nil
    }
    
    // 标准初始化方法
    init(deviceId: String, autoOtaEnabled: Bool, otaTimeWindow: OTATimeWindow, betaUpdates: Bool, lastCheck: String?, nextCheck: String?, idleUpdateStartHour: Int?, idleUpdateEndHour: Int?) {
        self.deviceId = deviceId
        self.autoOtaEnabled = autoOtaEnabled
        self.otaTimeWindow = otaTimeWindow
        self.betaUpdates = betaUpdates
        self.lastCheck = lastCheck
        self.nextCheck = nextCheck
        self.idleUpdateStartHour = idleUpdateStartHour
        self.idleUpdateEndHour = idleUpdateEndHour
    }
}

/// 简单的OTA设置格式（实际API返回）
struct SimpleOTASettings: Codable {
    let autoOtaUpgrade: String
    
    enum CodingKeys: String, CodingKey {
        case autoOtaUpgrade = "auto_ota_upgrade"
    }
}

/// OTA设置更新请求
struct DeviceOTAUpdateRequest: Codable {
    let autoOtaEnabled: Bool
    let otaTimeWindow: OTATimeWindow
    let betaUpdates: Bool
    
    // 🆕 新增：闲时更新时间
    let idleUpdateStartHour: Int?
    let idleUpdateEndHour: Int?
    
    enum CodingKeys: String, CodingKey {
        case autoOtaEnabled = "auto_ota_enabled"
        case otaTimeWindow = "ota_time_window"
        case betaUpdates = "beta_updates"
        case idleUpdateStartHour = "idle_update_start_hour"
        case idleUpdateEndHour = "idle_update_end_hour"
    }
}

/// 简单的OTA更新请求（匹配实际API格式）
struct SimpleOTAUpdateRequest: Codable {
    let autoOtaUpgrade: String
    
    // 🆕 新增：闲时更新时间设置
    let idleUpdateStartHour: Int?
    let idleUpdateEndHour: Int?
    
    enum CodingKeys: String, CodingKey {
        case autoOtaUpgrade = "auto_ota_upgrade"
        case idleUpdateStartHour = "idle_update_start_hour"
        case idleUpdateEndHour = "idle_update_end_hour"
    }
    
    init(enabled: Bool, idleUpdateStartHour: Int? = nil, idleUpdateEndHour: Int? = nil) {
        self.autoOtaUpgrade = enabled ? "on" : "off"
        self.idleUpdateStartHour = idleUpdateStartHour
        self.idleUpdateEndHour = idleUpdateEndHour
    }
}

/// API响应包装
struct OTASettingsResponse: Codable {
    let status: String
    let message: String
    let data: DeviceOTASettings
}

// MARK: - 传感器状态详细视图
struct SensorStatusDetailView: View {
    let deviceId: String
    @Environment(\.colorScheme) var colorScheme
    @EnvironmentObject private var deviceManager: DeviceAndGroupManager
    
    var body: some View {
        if let sensorStatus = deviceManager.getSensorStatus(for: deviceId) {
            VStack(alignment: .leading, spacing: 8) {
                // 传感器状态总览
                HStack {
                    if sensorStatus.hasErrors {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                        Text("检测到 \(sensorStatus.errorCount) 个传感器异常")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.red)
                    } else {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text("所有传感器状态正常")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                    }
                    Spacer()
                }
                .padding(.bottom, 4)
                
                // 分组显示：异常传感器和正常传感器
                if sensorStatus.hasErrors {
                    // 异常传感器组
                    VStack(alignment: .leading, spacing: 4) {
                        Text("异常传感器")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.red)
                            .padding(.bottom, 2)
                        
                        ForEach(SensorType.allCases, id: \.rawValue) { sensorType in
                            if let sensorError = sensorStatus.sensorStatus(for: sensorType), sensorError.isError {
                                SensorDetailRow(sensorError: sensorError)
                            }
                        }
                    }
                    .padding(.bottom, 8)
                    
                    // 正常传感器组
                    VStack(alignment: .leading, spacing: 4) {
                        Text("正常传感器")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                            .padding(.bottom, 2)
                        
                        ForEach(SensorType.allCases, id: \.rawValue) { sensorType in
                            if let sensorError = sensorStatus.sensorStatus(for: sensorType), !sensorError.isError {
                                SensorDetailRow(sensorError: sensorError)
                            }
                        }
                    }
                } else {
                    // 所有传感器都正常时，显示简洁列表
                    ForEach(SensorType.allCases, id: \.rawValue) { sensorType in
                        SensorDetailRow(sensorType: sensorType, isNormal: true)
                    }
                }
                
                // 最后更新时间
                if !sensorStatus.lastUpdated.isEmpty {
                    HStack {
                        Spacer()
                        Text("最后更新: \(sensorStatus.formattedLastUpdated)")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                    .padding(.top, 8)
                }
            }
        } else if deviceManager.isFetchingSensorStatus.contains(deviceId) {
            HStack {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                Text("正在获取传感器状态...")
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            }
            .padding(.vertical, 8)
        } else if let error = deviceManager.sensorStatusErrors[deviceId] {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.orange)
                    Text("获取传感器状态失败")
                        .foregroundColor(.orange)
                        .fontWeight(.medium)
                }
                
                Text(error.localizedDescription)
                    .font(.caption)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            }
            .padding(.vertical, 4)
        } else {
            HStack {
                Image(systemName: "sensor.tag.radiowaves.forward.fill")
                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                Text("点击刷新传感器获取状态")
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            }
            .padding(.vertical, 8)
        }
    }
}

// MARK: - 传感器详细行视图
struct SensorDetailRow: View {
    let sensorError: SensorError?
    let sensorType: SensorType?
    let isNormal: Bool
    @Environment(\.colorScheme) var colorScheme
    
    init(sensorError: SensorError) {
        self.sensorError = sensorError
        self.sensorType = nil
        self.isNormal = false
    }
    
    init(sensorType: SensorType, isNormal: Bool) {
        self.sensorError = nil
        self.sensorType = sensorType
        self.isNormal = isNormal
    }
    
    var body: some View {
        let sensor = sensorError?.sensorType ?? sensorType!
        
        VStack(alignment: .leading, spacing: 6) {
            HStack(spacing: 12) {
                // 传感器图标
                Image(systemName: sensor.iconName)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(statusColor)
                    .frame(width: 24, height: 24)
                
                // 传感器名称
                Text(sensor.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                Spacer()
                
                // 状态标签
                HStack(spacing: 4) {
                    Circle()
                        .fill(statusColor)
                        .frame(width: 8, height: 8)
                    
                    Text(statusText)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(statusColor)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(statusColor.opacity(0.1))
                )
            }
            
            // 详细错误信息（如果有）
            if let error = sensorError, error.isError {
                VStack(alignment: .leading, spacing: 4) {
                    // 错误类型信息
                    HStack {
                        Image(systemName: "exclamationmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.red)
                        
                        Text("错误类型: \(error.errorType ?? "未知")")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                    .padding(.leading, 36) // 与图标对齐
                    
                    // 错误时间信息
                    if let errorTime = error.errorTime, !errorTime.isEmpty {
                        HStack {
                            Image(systemName: "clock.fill")
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            
                            Text(formatErrorTime(errorTime))
                                .font(.caption)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        }
                        .padding(.leading, 36) // 与图标对齐
                    }
                }
                .padding(.top, 2)
            }
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(sensorError?.isError == true ? Color.red.opacity(0.05) : Color.clear)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(sensorError?.isError == true ? Color.red.opacity(0.2) : Color.clear, lineWidth: 1)
        )
    }
    
    private var statusColor: Color {
        if isNormal {
            return .green
        } else if let error = sensorError {
            return error.isError ? .red : .green
        } else {
            return .gray
        }
    }
    
    private var statusText: String {
        if isNormal {
            return "正常"
        } else if let error = sensorError {
            return error.isError ? "异常" : "正常"
        } else {
            return "未知"
        }
    }
    
    private func formatErrorTime(_ timeString: String) -> String {
        // 尝试解析ISO8601格式的时间
        let formatter = ISO8601DateFormatter()
        if let date = formatter.date(from: timeString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MM-dd HH:mm"
            return "最后异常: \(displayFormatter.string(from: date))"
        } else {
            return "最后异常: \(timeString)"
        }
    }
}

// MARK: - 添加设备到家庭组表单
struct AddDeviceToGroupSheet: View {
    let device: DeviceViewModel
    let groups: [FamilyGroup]
    @Binding var selectedGroupId: String
    @Binding var isLoading: Bool
    @Binding var errorMessage: String?
    @Binding var showSuccess: Bool
    
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("设备信息")) {
                    HStack {
                        Text("设备名称")
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        Spacer()
                        Text(device.name)
                            .fontWeight(.medium)
                    }
                    
                    HStack {
                        Text("设备型号")
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        Spacer()
                        Text(device.model)
                    }
                }
                
                Section(header: Text("选择家庭组")) {
                    if groups.isEmpty {
                        Text("暂无可用的家庭组")
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.vertical, 8)
                    } else {
                        ForEach(groups.filter { $0.role == .owner }) { group in
                            Button(action: {
                                selectedGroupId = group.id
                            }) {
                                HStack {
                                    VStack(alignment: .leading, spacing: 4) {
                                        Text(group.name)
                                            .font(.headline)
                                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                                        
                                        if !group.description.isEmpty {
                                            Text(group.description)
                                                .font(.caption)
                                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                        }
                                        
                                        Text("\(group.memberCount) 成员 · \(group.deviceCount) 设备")
                                            .font(.caption)
                                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                    }
                                    
                                    Spacer()
                                    
                                    if selectedGroupId == group.id {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                                    } else {
                                        Image(systemName: "circle")
                                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                    }
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                
                if let error = errorMessage {
                    Section {
                        Text(error)
                            .foregroundColor(.red)
                            .font(.footnote)
                    }
                }
                
                if !selectedGroupId.isEmpty {
                    Section {
                        Button(action: addDeviceToGroup) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle())
                                        .scaleEffect(0.8)
                                    Text("添加中...")
                                } else {
                                    Text("添加到家庭组")
                                        .fontWeight(.medium)
                                }
                            }
                            .frame(maxWidth: .infinity, alignment: .center)
                            .foregroundColor(AppTheme.buttonTextColor(for: colorScheme))
                            .padding(.vertical, 10)
                            .background(AppTheme.buttonColor(for: colorScheme))
                            .cornerRadius(8)
                        }
                        .disabled(isLoading)
                    }
                }
            }
            .navigationTitle("添加到家庭组")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
    
    private func addDeviceToGroup() {
        guard !selectedGroupId.isEmpty else {
            errorMessage = "请选择家庭组"
            return
        }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            let manager = DeviceAndGroupManager()
            do {
                try await manager.addDeviceToGroup(deviceId: device.id, groupId: selectedGroupId)
                await MainActor.run {
                    self.showSuccess = true
                    self.isLoading = false
                    self.presentationMode.wrappedValue.dismiss()
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "添加失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
}
