import SwiftUI
import Foundation

struct HomeView: View {
    @Environment(\.colorScheme) var colorScheme
    @StateObject private var homeViewModel = HomeViewModel()
    @StateObject private var chartState = ChartViewState()
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 24) {
                    // 重点关注卡片 - 仅在有问题时显示
                    if !homeViewModel.alertCats.isEmpty {
                        AlertCard(alertCats: homeViewModel.alertCats)
                            .padding(.horizontal, 20)
                    }

                    // 每只猫的折线图卡片
                    ForEach(homeViewModel.catChartData, id: \.catId) { chartData in
                        CatChartCard(chartData: chartData, chartState: chartState)
                            .padding(.horizontal, 20)
                    }

                    // Loading/Calculating indicators
                    if homeViewModel.isCalculating {
                        CalculatingIndicator()
                            .padding(.horizontal, 20)
                    } else if homeViewModel.isLoading && homeViewModel.catChartData.isEmpty {
                        LoadingIndicator()
                            .padding(.horizontal, 20)
                    } else if homeViewModel.catChartData.isEmpty && homeViewModel.alertCats.isEmpty && !homeViewModel.isLoading && !homeViewModel.isCalculating {
                        FirstTimeUserEmptyStateView()
                            .padding(.horizontal, 20)
                    }

                    // 底部间距
                    Spacer(minLength: 20)
                }
                .padding(.top, 20)
            }
            .background(AppTheme.backgroundColor(for: colorScheme))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(NSLocalizedString("home_title", comment: ""))
                        .font(.headline)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                }
            }
            .refreshable {
                await homeViewModel.refreshData()
            }
        }
        .task {
            await homeViewModel.loadInitialData()
            // 预加载常用时间段的数据
            await homeViewModel.preloadCommonData()
        }
        .onReceive(NotificationCenter.default.publisher(for: .authenticationStatusChanged)) { _ in
            // 当认证状态改变时，重新加载数据
            Task {
                await homeViewModel.refreshData()
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .dataRefreshedOnForeground)) { _ in
            // 当应用进入前台且数据已刷新时，重新计算统计数据
            Task {
                await homeViewModel.refreshData()
            }
        }
        .onChange(of: chartState.selectedPeriod) { _, newPeriod in
            // 当时间段改变时，更新图表数据
            Task {
                await homeViewModel.updateChartData(
                    dataType: chartState.selectedDataType,
                    timePeriod: newPeriod,
                    dateRange: chartState.currentDateRange
                )
            }
        }
        .onChange(of: chartState.selectedDataType) { _, newDataType in
            // 当数据类型改变时，更新图表数据
            Task {
                await homeViewModel.updateChartData(
                    dataType: newDataType,
                    timePeriod: chartState.selectedPeriod,
                    dateRange: chartState.currentDateRange
                )
            }
        }
        .onChange(of: chartState.customDateRange) { _, _ in
            // 当自定义日期范围改变时，更新图表数据
            Task {
                await homeViewModel.updateChartData(
                    dataType: chartState.selectedDataType,
                    timePeriod: chartState.selectedPeriod,
                    dateRange: chartState.currentDateRange
                )
            }
        }
    }
}

// MARK: - Home View Model
@MainActor
class HomeViewModel: ObservableObject {
    @Published var catStats: [CatStatistics] = []
    @Published var catChartData: [CatChartData] = []
    @Published var alertCats: [CatAlert] = []
    @Published var isLoading = false
    @Published var isCalculating = false
    @Published var lastUpdateTime: Date?

    private let catManager = CatManager.shared
    private let chartDataService = ChartDataService.shared
    
    func loadInitialData() async {
        isLoading = true
        
        Log.info("🏠 HomeView: 开始加载初始数据")
        
        do {
            // 1. 首先确保共享VideoPlayerViewModel的初始数据已加载
            await VideoPlayerViewModel.shared.ensureInitialDataLoaded()
            
            // 2. 等待VideoPlayerViewModel完全准备就绪
            await waitForVideoPlayerDataReady()
            
            // 3. 强制刷新猫咪数据以获取最新头像信息
            await catManager.refreshAllCats(forceRefresh: true)
            
            // 4. 计算统计数据（使用最新的数据）
            await calculateStatistics()
            
            Log.info("🏠 HomeView: 初始数据加载完成")
        }
        
        isLoading = false
    }
    
    /// 等待VideoPlayerViewModel数据准备就绪
    private func waitForVideoPlayerDataReady() async {
        let maxWaitTime: TimeInterval = 10.0 // 最多等待10秒
        let checkInterval: TimeInterval = 0.5 // 每0.5秒检查一次
        let startTime = Date()
        
        while Date().timeIntervalSince(startTime) < maxWaitTime {
            let cachedData = VideoPlayerViewModel.shared.getCachedDataForStatistics()
            
            // 检查VideoPlayerViewModel是否有足够的数据
            if cachedData.segments.count > 0 || !VideoPlayerViewModel.shared.isInitialLoading {
                Log.info("🏠 HomeView: VideoPlayerViewModel数据准备就绪，segments: \(cachedData.segments.count)")
                return
            }
            
            Log.debug("🏠 HomeView: 等待VideoPlayerViewModel数据准备就绪...")
            try? await Task.sleep(nanoseconds: UInt64(checkInterval * 1_000_000_000))
        }
        
        Log.warning("⚠️ HomeView: VideoPlayerViewModel数据等待超时，继续进行统计计算")
    }
    
    func refreshData() async {
        // 检查是否需要刷新（避免频繁调用）
        if let lastUpdate = lastUpdateTime,
           Date().timeIntervalSince(lastUpdate) < HomeConstants.refreshIntervalSeconds {
            Log.info("🏠 HomeView: 距离上次刷新时间太短，跳过刷新")
            return
        }
        
        Log.info("🏠 HomeView: 开始刷新数据")
        
        do {
            // 刷新猫咪数据
            await catManager.refreshAllCats()
            
            // 重新计算统计数据（使用VideoPlayerViewModel的缓存数据）
            await calculateStatistics()
            
            // 更新刷新时间
            lastUpdateTime = Date()
            
            Log.info("🏠 HomeView: 数据刷新完成")
        }
    }
    
    private func calculateStatistics() async {
        isCalculating = true
        
        defer {
            // 无论成功还是失败，都确保重置计算状态
            isCalculating = false
        }
        
        // 添加临时解决方案说明
        Log.warning("⚠️ HomeView: 当前使用临时解决方案 - 如果API未返回animal_id，将基于体重和时长数据识别如厕记录")
        
        do {
            // 只显示正常状态的猫咪，不包括隐藏的猫咪
            let allCats = catManager.cats
            var newCatStats: [CatStatistics] = []
            var newAlertCats: [CatAlert] = []
            
            Log.info("🏠 正在计算 \(allCats.count) 只猫咪的统计数据（不包括隐藏的猫咪）")
            
            // 获取共享VideoPlayerViewModel的缓存数据
            let cachedData = VideoPlayerViewModel.shared.getCachedDataForStatistics()
            Log.info("🏠 VideoPlayerViewModel缓存中有 \(cachedData.segments.count) 个视频段")
            Log.info("🏠 VideoPlayerViewModel缓存中有 \(cachedData.catsCache.count) 只猫咪数据")
            
            // 如果缓存为空，尝试重新获取数据
            if cachedData.segments.isEmpty && allCats.count > 0 {
                Log.warning("⚠️ VideoPlayerViewModel缓存为空，尝试等待数据加载完成...")
                await waitForVideoPlayerDataReady()
                // 重新获取缓存数据
                let updatedCachedData = VideoPlayerViewModel.shared.getCachedDataForStatistics()
                Log.info("🏠 重新获取后VideoPlayerViewModel缓存中有 \(updatedCachedData.segments.count) 个视频段")
            }
            
            let now = Date()
            let twentyFourHoursAgo = HomeConstants.currentPeriodStart
            let fortyEightHoursAgo = HomeConstants.comparisonPeriodStart
            let eightHoursAgo = HomeConstants.toiletAlertThreshold
            
            for cat in allCats {
                Log.info("🐱 正在计算猫咪 \(cat.name) (\(cat.id)) 的统计数据")
                
                // 获取该猫的如厕记录（使用共享实例）
                let segments = await getToiletSegments(for: cat.id, from: fortyEightHoursAgo, to: now)
                
                Log.info("🐱 猫咪 \(cat.name) 在过去48小时共有 \(segments.count) 次如厕记录")
                
                // 打印一些段的详细信息用于调试
                if !segments.isEmpty {
                    Log.info("🐱 猫咪 \(cat.name) 的最新如厕记录: \(segments.prefix(3).map { "时间:\($0.start), 体重:\($0.weight_cat)kg, 时长:\($0.duration)s" }.joined(separator: "; "))")
                }
                
                // 分离最近24小时和前24小时的数据
                let recentSegments = segments.filter { $0.start >= twentyFourHoursAgo }
                let previousSegments = segments.filter { $0.start >= fortyEightHoursAgo && $0.start < twentyFourHoursAgo }
                
                // 检查是否需要警告（8小时未如厕）
                let lastToiletTime = segments.first?.start
                if let lastTime = lastToiletTime, lastTime < eightHoursAgo {
                    let hoursAgo = Int(now.timeIntervalSince(lastTime) / 3600)
                    newAlertCats.append(CatAlert(
                        catId: cat.id,
                        catName: cat.name,
                        avatarUrl: cat.avatarUrl,
                        lastToiletTime: lastTime,
                        hoursWithoutToilet: hoursAgo
                    ))
                } else if segments.isEmpty {
                    // 如果完全没有记录，也算作警告
                    newAlertCats.append(CatAlert(
                        catId: cat.id,
                        catName: cat.name,
                        avatarUrl: cat.avatarUrl,
                        lastToiletTime: nil,
                        hoursWithoutToilet: nil
                    ))
                    Log.debug("🐱 猫咪 \(cat.name) 没有如厕记录，添加到警告列表")
                }
                
                // 计算统计数据
                let recentCount = recentSegments.count
                let recentDuration = recentSegments.reduce(0) { $0 + $1.duration }
                let previousCount = previousSegments.count
                let previousDuration = previousSegments.reduce(0) { $0 + $1.duration }
                
                // 计算体重变化
                let currentWeight = recentSegments.first?.weight_cat
                let previousWeight = findNearestWeight(around: twentyFourHoursAgo, in: segments)
                
                var weightChange: Double?
                var weightChangePercentage: Double?
                
                if let current = currentWeight, let previous = previousWeight, previous > 0 {
                    weightChange = current - previous
                    weightChangePercentage = (weightChange! / previous) * 100
                }
                
                // 优先使用最新的猫咪头像数据，确保首次进入时显示正确
                let avatarUrl = cat.avatarUrl
                
                let catStat = CatStatistics(
                    catId: cat.id,
                    catName: cat.name,
                    avatarUrl: avatarUrl,
                    recentToiletCount: recentCount,
                    recentTotalDuration: recentDuration,
                    previousToiletCount: previousCount,
                    previousTotalDuration: previousDuration,
                    currentWeight: currentWeight,
                    previousWeight: previousWeight,
                    weightChange: weightChange,
                    weightChangePercentage: weightChangePercentage
                )
                
                newCatStats.append(catStat)
            }
            
            self.catStats = newCatStats
            self.alertCats = newAlertCats

            Log.info("🏠 统计计算完成: \(newCatStats.count) 只猫咪的统计数据, \(newAlertCats.count) 只猫咪需要关注")

            // 生成图表数据
            await generateChartData()
        }
    }

    /// 生成图表数据（使用智能缓存）
    private func generateChartData() async {
        Log.info("🔄 开始生成图表数据")

        let allCats = catManager.cats
        let defaultDataType = ChartDataType.toilet
        let defaultTimePeriod = ChartTimePeriod.day
        let defaultDateRange = DateRange.defaultRange(for: defaultTimePeriod)

        // 使用优化的批量生成方法
        let chartDataList = await chartDataService.generateChartDataForAllCats(
            cats: allCats,
            dataType: defaultDataType,
            timePeriod: defaultTimePeriod,
            dateRange: defaultDateRange
        )

        self.catChartData = chartDataList

        Log.info("✅ 图表数据生成完成: \(chartDataList.count) 只猫咪")

        // 预加载重量数据，确保切换时有数据可用
        Log.info("🔄 预加载重量数据...")
        let weightChartDataList = await chartDataService.generateChartDataForAllCats(
            cats: allCats,
            dataType: .weight,
            timePeriod: defaultTimePeriod,
            dateRange: defaultDateRange
        )
        Log.info("✅ 重量数据预加载完成: \(weightChartDataList.count) 只猫咪")

        // 调试：显示重量数据统计
        for weightData in weightChartDataList {
            let dataPointCount = weightData.weightDataPoints.count
            if dataPointCount > 0 {
                let avgWeight = weightData.averageWeight
                Log.info("📊 猫咪 \(weightData.catName) 重量数据: \(dataPointCount) 个数据点, 平均重量: \(String(format: "%.1f", avgWeight))kg")
            } else {
                Log.warning("⚠️ 猫咪 \(weightData.catName) 没有重量数据")
            }
        }

        // 定期清理过期缓存
        Task {
            await chartDataService.cleanupCache()
        }
    }

    /// 更新图表数据（当数据类型、时间段或日期范围改变时调用）
    func updateChartData(dataType: ChartDataType, timePeriod: ChartTimePeriod, dateRange: DateRange) async {
        Log.info("🔄 更新图表数据: \(dataType.displayName), \(timePeriod.rawValue), \(dateRange.formattedRange)")

        let allCats = catManager.cats

        // 预加载新时间段的数据
        await chartDataService.preloadDataForPeriod(timePeriod, for: allCats.map { $0.id })

        let chartDataList = await chartDataService.generateChartDataForAllCats(
            cats: allCats,
            dataType: dataType,
            timePeriod: timePeriod,
            dateRange: dateRange
        )

        self.catChartData = chartDataList

        Log.info("✅ 图表数据更新完成")
    }

    /// 预加载常用时间段的数据
    func preloadCommonData() async {
        Log.info("🔄 开始预加载常用时间段数据")

        let allCats = catManager.cats
        let catIds = allCats.map { $0.id }

        // 预加载天、周、月三种时间段的数据
        await chartDataService.preloadDataForPeriod(.day, for: catIds)
        await chartDataService.preloadDataForPeriod(.week, for: catIds)
        await chartDataService.preloadDataForPeriod(.month, for: catIds)

        Log.info("✅ 常用时间段数据预加载完成")
    }

    private func getToiletSegments(for catId: String, from startDate: Date, to endDate: Date) async -> [VideoSegment] {
        // 使用VideoPlayerViewModel共享实例的专用方法获取数据
        return await VideoPlayerViewModel.shared.getToiletSegments(for: catId, from: startDate, to: endDate)
    }
    
    private func findNearestWeight(around targetDate: Date, in segments: [VideoSegment]) -> Double? {
        let sorted = segments.sorted { abs($0.start.timeIntervalSince(targetDate)) < abs($1.start.timeIntervalSince(targetDate)) }
        return sorted.first?.weight_cat
    }
}

// MARK: - Data Models
struct CatStatistics {
    let catId: String
    let catName: String
    let avatarUrl: String?
    let recentToiletCount: Int
    let recentTotalDuration: TimeInterval
    let previousToiletCount: Int
    let previousTotalDuration: TimeInterval
    let currentWeight: Double?
    let previousWeight: Double?
    let weightChange: Double?
    let weightChangePercentage: Double?
    
    var countComparison: ComparisonResult {
        if recentToiletCount > previousToiletCount { return .increased }
        if recentToiletCount < previousToiletCount { return .decreased }
        return .same
    }
    
    var formattedDuration: String {
        let minutes = Int(recentTotalDuration) / 60
        return "\(minutes)" + NSLocalizedString("home_duration_minutes", comment: "")
    }
    
    var formattedWeightChange: String? {
        guard let percentage = weightChangePercentage else { return nil }
        let sign = percentage >= 0 ? "+" : ""
        return "\(sign)\(String(format: "%.1f", percentage))%"
    }
}

struct CatAlert {
    let catId: String
    let catName: String
    let avatarUrl: String?
    let lastToiletTime: Date?
    let hoursWithoutToilet: Int?
    
    var alertMessage: String {
        if let hours = hoursWithoutToilet {
            // 支持不同语言的语法
            let currentLanguage = Locale.current.language.languageCode?.identifier ?? "en"
            if currentLanguage == "zh" {
                // 中文：小黑 已经 8 小时没有如厕了
                return "\(catName) " + NSLocalizedString("home_alert_no_toilet_suffix", comment: "") + " \(hours) " + NSLocalizedString("home_alert_hours_suffix", comment: "")
            } else {
                // 英文：小黑 hasn't used the toilet for 8 hours
                return "\(catName) " + NSLocalizedString("home_alert_no_toilet_suffix", comment: "") + " \(hours) " + NSLocalizedString("home_alert_hours_suffix", comment: "")
            }
        } else {
            return "\(catName) " + NSLocalizedString("home_no_toilet_records", comment: "")
        }
    }
}

enum ComparisonResult {
    case increased, decreased, same
    
    var color: Color {
        switch self {
        case .increased: return .green
        case .decreased: return .orange
        case .same: return .gray
        }
    }
    
    var icon: String {
        switch self {
        case .increased: return "arrow.up.circle.fill"
        case .decreased: return "arrow.down.circle.fill"
        case .same: return "minus.circle.fill"
        }
    }
}

// MARK: - UI Components
struct AlertCard: View {
    let alertCats: [CatAlert]
    @Environment(\.colorScheme) var colorScheme
    @State private var avatarRefreshTrigger = 0
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                    .font(.title2)
                
                Text(NSLocalizedString("home_alert_title", comment: ""))
                    .font(.headline)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                Spacer()
            }
            
            ForEach(alertCats, id: \.catId) { alert in
                HStack(spacing: 12) {
                    CatAvatarView(
                        avatarUrl: alert.avatarUrl,
                        size: 40,
                        cornerRadius: 20,
                        refreshTrigger: avatarRefreshTrigger
                    )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(alert.catName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(AppTheme.textColor(for: colorScheme))
                        
                        Text(alert.alertMessage)
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                    
                    Spacer()
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.orange.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
        .onReceive(NotificationCenter.default.publisher(for: .avatarUpdated)) { _ in
            // 触发头像刷新
            avatarRefreshTrigger += 1
            Log.debug("🐱 收到头像更新通知，触发警告卡片头像刷新")
        }
    }
}

struct CatStatisticsCard: View {
    let catStat: CatStatistics
    @Environment(\.colorScheme) var colorScheme
    @State private var avatarRefreshTrigger = 0
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 猫咪信息头部
            HStack(spacing: 12) {
                CatAvatarView(
                    avatarUrl: catStat.avatarUrl,
                    size: 50,
                    cornerRadius: 25,
                    refreshTrigger: avatarRefreshTrigger
                )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(catStat.catName)
                        .font(.headline)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                    
                    Text(NSLocalizedString("home_toilet_stats_title", comment: ""))
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                }
                
                Spacer()
            }
            
            // 如厕统计
            HStack(spacing: 20) {
                StatisticItem(
                    title: NSLocalizedString("home_toilet_count", comment: ""),
                    value: "\(catStat.recentToiletCount)",
                    comparison: catStat.countComparison,
                    previousValue: "\(catStat.previousToiletCount)"
                )
                
                StatisticItem(
                    title: NSLocalizedString("home_toilet_duration", comment: ""),
                    value: catStat.formattedDuration,
                    comparison: .same,
                    previousValue: nil
                )
            }
            
            // 体重变化
            if let currentWeight = catStat.currentWeight,
               let weightChangeText = catStat.formattedWeightChange {
                WeightChangeView(
                    currentWeight: currentWeight,
                    weightChangeText: weightChangeText,
                    weightChangeColor: weightChangeColor,
                    colorScheme: colorScheme
                )
            }
        }
        .padding(20)
        .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppTheme.primaryColor(for: colorScheme).opacity(0.2), lineWidth: 1)
        )
        .onReceive(NotificationCenter.default.publisher(for: .avatarUpdated)) { _ in
            // 触发头像刷新
            avatarRefreshTrigger += 1
            Log.debug("🐱 收到头像更新通知，触发刷新: \(catStat.catName)")
        }
    }
    
    private var weightChangeColor: Color {
        guard let changePercentage = catStat.weightChangePercentage else { return .gray }
        
        // 使用HomeConstants中的阈值
        let absChange = abs(changePercentage)
        if absChange > HomeConstants.weightChangeThreshold {
            return .orange  // 超过阈值用橙色
        } else {
            return .gray    // 不超过阈值用灰色
        }
    }
}

struct WeightChangeView: View {
    let currentWeight: Double
    let weightChangeText: String
    let weightChangeColor: Color
    let colorScheme: ColorScheme
    
    @State private var showWeightInfo = false
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                HStack(spacing: 4) {
                    Text(NSLocalizedString("home_weight_change", comment: ""))
                        .font(.caption)
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    
                    // 提示符按钮
                    Button(action: {
                        showWeightInfo = true
                    }) {
                        Image(systemName: "info.circle")
                            .font(.caption2)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme).opacity(0.6))
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                HStack(spacing: 8) {
                    Text(String(format: "%.1fkg", currentWeight))
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                    
                    Text(weightChangeText)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(weightChangeColor)
                        .padding(.horizontal, 10)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(weightChangeColor.opacity(0.1))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(weightChangeColor.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
            }
            
            Spacer()
        }
        .alert(
            NSLocalizedString("home_weight_change_info_title", comment: ""),
            isPresented: $showWeightInfo
        ) {
            Button("OK") { }
        } message: {
            Text(NSLocalizedString("home_weight_change_info_message", comment: ""))
        }
    }
}

struct StatisticItem: View {
    let title: String
    let value: String
    let comparison: ComparisonResult
    let previousValue: String?
    
    @Environment(\.colorScheme) var colorScheme
    @State private var showComparisonInfo = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
            
            HStack(spacing: 8) {
                Text(value)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                if let previous = previousValue {
                    HStack(spacing: 4) {
                        Image(systemName: comparison.icon)
                            .font(.caption)
                            .foregroundColor(smartComparisonColor)
                        
                        Text(NSLocalizedString("home_comparison_vs", comment: ""))
                            .font(.caption2)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        
                        Text(previous)
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        
                        // 提示符按钮
                        Button(action: {
                            showComparisonInfo = true
                        }) {
                            Image(systemName: "info.circle")
                                .font(.caption2)
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme).opacity(0.6))
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .alert(
            NSLocalizedString("home_comparison_info_title", comment: ""),
            isPresented: $showComparisonInfo
        ) {
            Button("OK") { }
        } message: {
            Text(NSLocalizedString("home_comparison_info_message", comment: ""))
        }
    }
    
    // 智能颜色逻辑
    private var smartComparisonColor: Color {
        guard let previous = previousValue, let previousInt = Int(previous) else {
            return .gray
        }
        
        let current = Int(value) ?? 0
        let change = abs(current - previousInt)
        
        // 使用HomeConstants中的阈值
        if change > HomeConstants.toiletCountChangeThreshold {
            return .orange  // 超过阈值用橙色
        } else {
            return .gray    // 不超过阈值用灰色
        }
    }
}

struct LoadingIndicator: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text(NSLocalizedString("home_loading_data", comment: ""))
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(height: 100)
    }
}

struct CalculatingIndicator: View {
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
                .tint(AppTheme.primaryColor(for: colorScheme))
            
            VStack(spacing: 8) {
                Text(NSLocalizedString("home_calculating_stats", comment: ""))
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                
                Text(NSLocalizedString("home_calculating_message", comment: ""))
                    .font(.caption)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    .multilineTextAlignment(.center)
            }
        }
        .frame(height: 120)
        .padding(.horizontal, 32)
    }
}

struct FirstTimeUserEmptyStateView: View {
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 16) {
                Image(systemName: "cat.circle")
                    .font(.system(size: 80))
                    .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                
                Text(NSLocalizedString("home_welcome_title", comment: "欢迎使用 CabyCare"))
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(AppTheme.textColor(for: colorScheme))
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 12) {
                Text(NSLocalizedString("home_welcome_message", comment: "开始监测您的爱猫健康"))
                    .font(.body)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    .multilineTextAlignment(.center)
                
                Text(NSLocalizedString("home_welcome_instruction", comment: "请确保设备已连接并有猫咪使用记录"))
                    .font(.caption)
                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    .multilineTextAlignment(.center)
            }
            
            // 添加一些提示步骤
            VStack(alignment: .leading, spacing: 12) {
                HStack(spacing: 12) {
                    Image(systemName: "1.circle.fill")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Text(NSLocalizedString("home_setup_step1", comment: "确保设备已正确连接"))
                        .font(.subheadline)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                    Spacer()
                }
                
                HStack(spacing: 12) {
                    Image(systemName: "2.circle.fill")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Text(NSLocalizedString("home_setup_step2", comment: "添加您的猫咪档案"))
                        .font(.subheadline)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                    Spacer()
                }
                
                HStack(spacing: 12) {
                    Image(systemName: "3.circle.fill")
                        .foregroundColor(AppTheme.primaryColor(for: colorScheme))
                    Text(NSLocalizedString("home_setup_step3", comment: "等待猫咪使用设备"))
                        .font(.subheadline)
                        .foregroundColor(AppTheme.textColor(for: colorScheme))
                    Spacer()
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 20)
            .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
            .cornerRadius(12)
        }
        .padding(.horizontal, 24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    Group {
        HomeView()
            .preferredColorScheme(.light)
        HomeView()
            .preferredColorScheme(.dark)
    }
}
