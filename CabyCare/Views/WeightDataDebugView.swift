import SwiftUI

struct WeightDataDebugView: View {
    @StateObject private var chartDataService = ChartDataService.shared
    @StateObject private var catManager = CatManager.shared
    @State private var debugInfo: [String] = []
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("检查重量数据...")
                        .padding()
                } else {
                    List(debugInfo, id: \.self) { info in
                        Text(info)
                            .font(.system(.caption, design: .monospaced))
                    }
                }
                
                VStack(spacing: 10) {
                    Button("检查重量数据") {
                        Task {
                            await checkWeightData()
                        }
                    }

                    Button("检查API原始数据") {
                        Task {
                            await checkAPIData()
                        }
                    }

                    Button("清除缓存并重新检查") {
                        Task {
                            await clearCacheAndRecheck()
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("重量数据调试")
        }
        .task {
            await checkWeightData()
        }
    }
    
    private func checkWeightData() async {
        isLoading = true
        debugInfo.removeAll()
        
        let cats = catManager.cats
        debugInfo.append("🐱 找到 \(cats.count) 只猫咪")
        
        let dateRange = DateRange.defaultRange(for: .day)
        debugInfo.append("📅 检查时间范围: \(dateRange.formattedRange)")
        
        for cat in cats {
            debugInfo.append("\n--- 猫咪: \(cat.name) (ID: \(cat.id)) ---")
            
            // 检查如厕数据
            let toiletData = await chartDataService.generateChartData(
                for: cat,
                dataType: .toilet,
                timePeriod: .day,
                dateRange: dateRange
            )
            debugInfo.append("🚽 如厕数据点: \(toiletData.toiletDataPoints.count)")
            
            // 检查重量数据
            let weightData = await chartDataService.generateChartData(
                for: cat,
                dataType: .weight,
                timePeriod: .day,
                dateRange: dateRange
            )
            debugInfo.append("⚖️ 重量数据点: \(weightData.weightDataPoints.count)")
            
            if !weightData.weightDataPoints.isEmpty {
                let avgWeight = weightData.averageWeight
                let minWeight = weightData.minWeight
                let maxWeight = weightData.maxWeight
                debugInfo.append("📊 平均重量: \(String(format: "%.1f", avgWeight))kg")
                debugInfo.append("📊 重量范围: \(String(format: "%.1f", minWeight)) - \(String(format: "%.1f", maxWeight))kg")
            } else {
                debugInfo.append("⚠️ 没有重量数据")
            }
            
            // 检查原始数据
            let segments = await VideoPlayerViewModel.shared.getToiletSegments(
                for: cat.id,
                from: dateRange.startDate,
                to: dateRange.endDate
            )
            
            let allWeights = segments.compactMap { segment in
                segment.weight_cat > 0 ? segment.weight_cat : nil
            }
            let validWeights = allWeights.filter { $0 > 0.1 && $0 < 50.0 }
            
            debugInfo.append("📊 原始视频段: \(segments.count)")
            debugInfo.append("📊 有重量数据: \(allWeights.count)")
            debugInfo.append("📊 有效重量数据: \(validWeights.count)")
            
            if !validWeights.isEmpty {
                let avgRawWeight = validWeights.reduce(0.0, +) / Double(validWeights.count)
                debugInfo.append("📊 原始平均重量: \(String(format: "%.1f", avgRawWeight))kg")
                debugInfo.append("📊 原始重量范围: \(String(format: "%.1f", validWeights.min() ?? 0)) - \(String(format: "%.1f", validWeights.max() ?? 0))kg")
            }

            // 显示前几个视频段的详细信息
            if !segments.isEmpty {
                debugInfo.append("📋 前5个视频段详情:")
                for (index, segment) in segments.prefix(5).enumerated() {
                    debugInfo.append("  \(index+1). 时间: \(segment.start.formattedString())")
                    debugInfo.append("     重量: \(segment.weight_cat)kg")
                    debugInfo.append("     时长: \(segment.duration)s")
                    debugInfo.append("     猫咪ID: \(segment.animal_id ?? "nil")")
                }
            }
        }

        isLoading = false
    }

    private func checkAPIData() async {
        isLoading = true
        debugInfo.removeAll()

        debugInfo.append("🔍 直接检查API数据...")

        let cats = catManager.cats
        let dateRange = DateRange.defaultRange(for: .day)

        for cat in cats {
            debugInfo.append("\n--- API数据检查: \(cat.name) ---")

            // 直接调用VideoPlayerViewModel获取数据
            let segments = await VideoPlayerViewModel.shared.getToiletSegments(
                for: cat.id,
                from: dateRange.startDate,
                to: dateRange.endDate
            )

            debugInfo.append("📊 API返回视频段: \(segments.count)")

            let allWeights = segments.compactMap { segment in
                segment.weight_cat > 0 ? segment.weight_cat : nil
            }
            let validWeights = allWeights.filter { $0 > 0.1 && $0 < 50.0 }

            debugInfo.append("📊 有重量数据: \(allWeights.count)")
            debugInfo.append("📊 有效重量数据: \(validWeights.count)")

            if !validWeights.isEmpty {
                let avgWeight = validWeights.reduce(0.0, +) / Double(validWeights.count)
                debugInfo.append("📊 重量统计: 平均=\(String(format: "%.1f", avgWeight))kg")
                debugInfo.append("📊 重量范围: \(String(format: "%.1f", validWeights.min() ?? 0)) - \(String(format: "%.1f", validWeights.max() ?? 0))kg")
            }

            // 显示前几个有重量的段
            let weightSegments = segments.filter { $0.weight_cat > 0 }.prefix(3)
            for (index, segment) in weightSegments.enumerated() {
                debugInfo.append("  样本\(index+1): \(segment.start.formattedString()) - \(segment.weight_cat)kg")
            }
        }

        isLoading = false
    }

    private func clearCacheAndRecheck() async {
        isLoading = true
        debugInfo.removeAll()

        debugInfo.append("🗑️ 清除缓存并重新检查...")

        // 清除VideoPlayerViewModel的缓存
        await VideoPlayerViewModel.shared.clearAllCache()

        // 清除ChartDataService的缓存
        await chartDataService.cleanupCache()

        debugInfo.append("✅ 缓存已清除")

        // 重新检查数据
        await checkWeightData()

        isLoading = false
    }
}

struct WeightDataDebugView_Previews: PreviewProvider {
    static var previews: some View {
        WeightDataDebugView()
    }
}
