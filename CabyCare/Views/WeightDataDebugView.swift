import SwiftUI

struct WeightDataDebugView: View {
    @StateObject private var chartDataService = ChartDataService.shared
    @StateObject private var catManager = CatManager.shared
    @State private var debugInfo: [String] = []
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("检查重量数据...")
                        .padding()
                } else {
                    List(debugInfo, id: \.self) { info in
                        Text(info)
                            .font(.system(.caption, design: .monospaced))
                    }
                }
                
                Button("检查重量数据") {
                    Task {
                        await checkWeightData()
                    }
                }
                .padding()
            }
            .navigationTitle("重量数据调试")
        }
        .task {
            await checkWeightData()
        }
    }
    
    private func checkWeightData() async {
        isLoading = true
        debugInfo.removeAll()
        
        let cats = catManager.cats
        debugInfo.append("🐱 找到 \(cats.count) 只猫咪")
        
        let dateRange = DateRange.defaultRange(for: .day)
        debugInfo.append("📅 检查时间范围: \(dateRange.formattedRange)")
        
        for cat in cats {
            debugInfo.append("\n--- 猫咪: \(cat.name) (ID: \(cat.id)) ---")
            
            // 检查如厕数据
            let toiletData = await chartDataService.generateChartData(
                for: cat,
                dataType: .toilet,
                timePeriod: .day,
                dateRange: dateRange
            )
            debugInfo.append("🚽 如厕数据点: \(toiletData.toiletDataPoints.count)")
            
            // 检查重量数据
            let weightData = await chartDataService.generateChartData(
                for: cat,
                dataType: .weight,
                timePeriod: .day,
                dateRange: dateRange
            )
            debugInfo.append("⚖️ 重量数据点: \(weightData.weightDataPoints.count)")
            
            if !weightData.weightDataPoints.isEmpty {
                let avgWeight = weightData.averageWeight
                let minWeight = weightData.minWeight
                let maxWeight = weightData.maxWeight
                debugInfo.append("📊 平均重量: \(String(format: "%.1f", avgWeight))kg")
                debugInfo.append("📊 重量范围: \(String(format: "%.1f", minWeight)) - \(String(format: "%.1f", maxWeight))kg")
            } else {
                debugInfo.append("⚠️ 没有重量数据")
            }
            
            // 检查原始数据
            let segments = await VideoPlayerViewModel.shared.getToiletSegments(
                for: cat.id,
                from: dateRange.startDate,
                to: dateRange.endDate
            )
            
            let allWeights = segments.compactMap { segment in
                segment.weight_cat > 0 ? segment.weight_cat : nil
            }
            let validWeights = allWeights.filter { $0 > 0.5 && $0 < 20.0 }
            
            debugInfo.append("📊 原始视频段: \(segments.count)")
            debugInfo.append("📊 有重量数据: \(allWeights.count)")
            debugInfo.append("📊 有效重量数据: \(validWeights.count)")
            
            if !validWeights.isEmpty {
                let avgRawWeight = validWeights.reduce(0.0, +) / Double(validWeights.count)
                debugInfo.append("📊 原始平均重量: \(String(format: "%.1f", avgRawWeight))kg")
                debugInfo.append("📊 原始重量范围: \(String(format: "%.1f", validWeights.min() ?? 0)) - \(String(format: "%.1f", validWeights.max() ?? 0))kg")
            }
        }
        
        isLoading = false
    }
}

struct WeightDataDebugView_Previews: PreviewProvider {
    static var previews: some View {
        WeightDataDebugView()
    }
}
