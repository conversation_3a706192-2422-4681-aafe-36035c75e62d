# CabyCare 折线图功能实现指南

## 概述

本文档描述了CabyCare iOS应用中新实现的折线图功能，该功能将原有的简单如厕次数显示改造为美观的交互式折线图界面。

## 功能特性

### 1. 时间段选择
- **天视图**：显示最近7天的每日如厕次数
- **周视图**：显示最近4周的每周如厕次数
- **月视图**：显示最近6个月的每月如厕次数
- **自定义范围**：用户可以选择任意时间范围

### 2. 交互式图表
- 点击数据点查看详细信息
- 平滑的动画过渡效果
- 趋势分析（上升/下降/稳定）
- 统计信息显示（总计、平均值、最大值等）

### 3. 美观的UI设计
- 每只猫独立的卡片布局
- 渐变背景和阴影效果
- 响应式设计，支持深色/浅色模式
- 使用应用主题色彩

## 技术架构

### 核心组件

#### 1. ChartModels.swift
定义了图表相关的数据模型：
- `ChartTimePeriod`：时间段枚举（天/周/月）
- `ToiletChartDataPoint`：图表数据点结构
- `CatChartData`：猫咪图表数据容器
- `ChartTrend`：趋势分析枚举
- `DateRange`：日期范围管理
- `ChartConfiguration`：图表配置
- `ChartViewState`：图表状态管理

#### 2. ChartDataService.swift
数据聚合和处理服务：
- 从VideoPlayerViewModel获取原始数据
- 按时间段聚合如厕记录
- 计算统计信息和趋势
- 支持批量处理多只猫咪的数据

#### 3. UI组件

##### TimePeriodSelector.swift
时间段选择器组件：
- 天/周/月按钮选择
- 自定义日期范围选择
- 快捷日期选择（最近7天、30天等）

##### ToiletLineChart.swift
折线图组件：
- 使用Swift Charts框架
- 支持数据点交互
- 动画效果和渐变填充
- 网格线和坐标轴

##### CatChartCard.swift
猫咪图表卡片：
- 集成所有组件
- 处理加载/错误/空数据状态
- 美观的卡片布局

## 使用方法

### 1. 在HomeView中使用

```swift
struct HomeView: View {
    @StateObject private var homeViewModel = HomeViewModel()
    @StateObject private var chartState = ChartViewState()
    
    var body: some View {
        // ... 其他代码
        
        ForEach(homeViewModel.catChartData, id: \.catId) { chartData in
            CatChartCard(chartData: chartData, chartState: chartState)
        }
    }
}
```

### 2. 数据更新

当时间段或日期范围改变时，HomeView会自动调用：

```swift
await homeViewModel.updateChartData(
    timePeriod: newPeriod,
    dateRange: newDateRange
)
```

### 3. 自定义配置

可以通过ChartConfiguration自定义图表外观：

```swift
let config = ChartConfiguration.themed(for: colorScheme)
```

## 数据流

1. **数据获取**：ChartDataService从VideoPlayerViewModel获取缓存的视频段数据
2. **数据过滤**：根据猫咪ID和时间范围过滤数据
3. **数据聚合**：按选择的时间段（天/周/月）聚合数据
4. **趋势分析**：计算数据趋势和统计信息
5. **UI更新**：将处理后的数据传递给图表组件显示

## 性能优化

- 复用VideoPlayerViewModel的缓存数据，避免重复网络请求
- 使用@MainActor确保UI更新在主线程
- 懒加载和按需计算，提高响应速度
- 合理的动画时长，平衡美观和性能

## 本地化支持

添加了以下本地化字符串：
- `chart_period_day` = "天" / "Day"
- `chart_period_week` = "周" / "Week"
- `chart_period_month` = "月" / "Month"
- `chart_trend_increasing` = "上升" / "Increasing"
- `chart_trend_decreasing` = "下降" / "Decreasing"
- `chart_trend_stable` = "稳定" / "Stable"

## 调试和测试

### 预览组件
使用`ChartPreviewView.swift`中的预览组件来测试：
- 完整图表预览
- 单个组件预览
- 调试界面

### 调试信息
在调试模式下可以查看：
- 当前选择的时间段
- 数据加载状态
- 错误信息
- 自定义日期范围

## 故障排除

### 常见问题

1. **数据不显示**
   - 检查VideoPlayerViewModel是否有数据
   - 确认时间范围内有如厕记录
   - 查看控制台日志

2. **图表不更新**
   - 确认ChartViewState的状态变化
   - 检查HomeViewModel的数据更新逻辑

3. **性能问题**
   - 检查数据聚合逻辑的效率
   - 确认没有不必要的重复计算

## 未来扩展

可以考虑添加的功能：
- 更多图表类型（柱状图、饼图等）
- 数据导出功能
- 更详细的统计分析
- 多猫咪对比视图
- 健康建议基于趋势分析

## 依赖

- iOS 17.6+
- Swift Charts (系统框架)
- SwiftUI
- Foundation

## 文件结构

```
CabyCare/
├── Models/
│   └── ChartModels.swift
├── Services/
│   └── ChartDataService.swift
├── Views/
│   ├── HomeView.swift (修改)
│   └── Components/
│       ├── TimePeriodSelector.swift
│       ├── ToiletLineChart.swift
│       ├── CatChartCard.swift
│       └── ChartPreviewView.swift
└── Resources/
    ├── en.lproj/Localizable.strings (更新)
    └── zh-Hans.lproj/Localizable.strings (更新)
```

这个实现提供了一个完整、美观且功能丰富的折线图界面，大大提升了用户查看和分析猫咪如厕数据的体验。
